{"name": "games", "version": "0.0.1", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "postinstall": "svelte-kit sync", "package": "svelte-kit package", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "jest", "lint": "prettier --check . && eslint .", "format": "prettier --write ."}, "devDependencies": {"@sveltejs/kit": "2.21.1", "@tailwindcss/typography": "^0.5.16", "@types/howler": "^2.2.12", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.20", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-config-prettier": "^9.1.2", "eslint-plugin-svelte": "^2.46.1", "jest": "^29.7.0", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "svelte": "5.33.10", "svelte-check": "^4.3.1", "svelte-eslint-parser": "^0.43.0", "svelte-preprocess": "^6.0.3", "ts-jest": "^29.4.1", "tslib": "^2.8.1", "typescript": "^5.9.2", "vite": "^6.3.5"}, "type": "module", "dependencies": {"@babel/preset-typescript": "^7.27.1", "@better-auth/stripe": "^1.3.4", "@emailjs/browser": "^4.4.1", "@number-flow/svelte": "^0.2.3", "@sentry/sveltekit": "^9.44.0", "@supabase/supabase-js": "^2.53.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/vite-plugin-svelte": "^5.1.1", "@tailwindcss/vite": "^4.1.11", "@tanstack/svelte-query": "^5.83.1", "@thi.ng/random": "^4.1.24", "@tweenjs/tween.js": "^25.0.0", "@types/cloudflare-turnstile": "^0.2.2", "@types/youtube": "^0.1.2", "autoprefixer": "^10.4.21", "better-auth": "^1.3.4", "clsx": "^2.1.1", "daisyui": "^5.0.50", "dexie": "^4.0.11", "dotenv": "^16.6.1", "excalibur": "^0.30.3", "howler": "^2.2.4", "js-confetti": "^0.12.0", "konva": "^9.3.22", "lodash": "^4.17.21", "minimaxer": "^3.4.0", "motion": "12.15.0", "pixi.js": "^8.11.0", "stripe": "^18.4.0", "svelte-copy": "^2.0.0", "svelte-konva": "1.0.0-next.5", "svelte-meta-tags": "^4.4.0", "svelte-portal": "^2.2.1", "svelte-share-buttons-component": "^3.0.0", "svelte-sonner": "^0.3.28", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}}