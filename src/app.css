@import 'tailwindcss';
@plugin "@tailwindcss/typography";
@plugin "daisyui";

@import "./themes/light.css";
@import "./themes/dark.css";
@import "./themes/light-color-blind.css";
@import "./themes/dark-color-blind.css";

@custom-variant dark (&:where([data-theme="dark"], [data-theme="dark"] *,
    [data-theme="dark-color-blind"], [data-theme="dark-color-blind"] *, [data-theme="dark-classic"], [data-theme="dark-classic"] *));

@custom-variant colorblind (&:is([data-color-blind="true"] *));

@layer base {

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@utility glass {
  background-image: unset;
  @apply bg-base-300/30 dark:bg-base-300/50;
}

@utility dropdown {
  & .dropdown-content {
    @apply bg-base-100 dark:bg-base-300;
  }
}

@utility dropdown-content {
  .dropdown & {
    @apply bg-base-100 dark:bg-base-300;
  }
}

@utility min-h-screen {
  min-height: var(--vh);
}

@utility h-screen {
  height: var(--vh);
}

@utility min-h-screen-no-navbar {
  min-height: calc(var(--vh) - var(--navbar-height));
}

@utility max-h-screen-no-navbar {
  max-height: calc(var(--vh) - var(--navbar-height));
}

@utility h-screen-no-navbar {
  height: calc(var(--vh) - var(--navbar-height));
}

@utility flex-center {
  @apply flex items-center justify-center;
}

@layer base {
  article {
    @apply container prose;
  }

  article p {
    @apply text-lg;
  }

  :root {
    --vh: 100vh;
  }
}

@layer utilities {
  :root {
    --navbar-height: 64px;
  }
}

html {
  scroll-behavior: smooth;
  /* Remove double tap to zoom */
  touch-action: manipulation;
  /* Prevent scrollbar showing when modal is open */
  scrollbar-gutter: auto !important;

  @variant dark {
    /* Fix scrollbar color on Safari */
    color-scheme: dark;
  }
}

* {
  user-select: none;
}

@theme {
  --breakpoint-xs: 25rem;
}