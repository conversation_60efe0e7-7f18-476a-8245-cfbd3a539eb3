@plugin "daisyui/theme" {
  name: "dark";
}

@plugin "daisyui/theme" {
  name: "dark-classic";
  color-scheme: dark;

  --color-base-100: #1d232a;
  --color-base-200: #191e24;
  --color-base-300: #15191e;
  --color-base-content: #A6ADBB;
  --color-primary: oklch(65.69% 0.196 275.75);
  --color-primary-content: oklch(0.13138 0.0392 275.75);
  --color-secondary: oklch(74.8% 0.26 342.55);
  --color-secondary-content: oklch(0.1496 0.052 342.55);
  --color-accent: oklch(74.51% 0.167 183.61);
  --color-accent-content: oklch(0.14902 0.0334 183.61);
  --color-neutral: #2a323c;
  --color-neutral-content: oklch(0.746477 0.0216 264.436);
  --color-info: oklch(74% 0.16 232.661);
  --color-info-content: oklch(0 0 0);
  --color-success: oklch(76% 0.177 163.223);
  --color-success-content: oklch(0 0 0);
  --color-warning: oklch(82% 0.189 84.429);
  --color-warning-content: oklch(0 0 0);
  --color-error: oklch(71% 0.194 13.428);
  --color-error-content: oklch(0 0 0);
  --radius-selector: 1.9rem;
  --radius-field: 0.5rem;
  --radius-box: 1rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 0;
  --noise: 0;
}

[data-theme="dark"],
[data-theme="dark-classic"] {
  --color-game-tents-floor: var(--color-gray-700);
  --color-game-tents-grass: var(--color-teal-800);
  --color-game-tents-grass-autofill: var(--color-teal-700);
  --color-game-tents-tree-leaves: var(--color-emerald-600);
  --color-game-tents-tree-wood: var(--color-yellow-700);
  --color-game-tents-tent-100: var(--color-amber-500);
  --color-game-tents-tent-200: var(--color-amber-700);
  --color-game-tents-tent-300: var(--color-amber-950);
  --color-game-tents-tent-error-100: var(--color-red-500);
  --color-game-tents-tent-error-200: var(--color-red-700);
  --color-game-tents-tent-error-300: var(--color-red-950);
  --color-game-tents-number-error: var(--color-red-500);
  --color-game-tents-number-success: var(--color-emerald-500);
  --color-game-tents-tent-error-indicator-stroke: var(--color-gray-300);
  --color-game-tents-tent-error-indicator: var(--color-red-600);
  --color-game-wordle-not-present: var(--color-gray-400);
  --color-game-wordle-not-present-hover: var(--color-gray-500);
  --color-game-wordle-correct: var(--color-green-500);
  --color-game-wordle-correct-hover: var(--color-green-600);
  --color-game-wordle-misplaced: var(--color-yellow-400);
  --color-game-wordle-misplaced-hover: var(--color-yellow-500);
  --color-game-wordle-validated-text: var(--color-white);
  --color-game-wordle-border: var(--color-gray-500);
  --color-game-solitaire-field: #219653;
  --color-game-breakout-tile-1: var(--color-red-400);
  --color-game-breakout-tile-2: var(--color-orange-400);
  --color-game-breakout-tile-3: var(--color-yellow-400);
  --color-game-breakout-tile-4: var(--color-green-400);
  --color-game-breakout-tile-5: var(--color-blue-400);
  --color-game-breakout-tile-6: var(--color-purple-400);
  --color-game-tic-tac-toe-x: #FF70A6;
  --color-game-tic-tac-toe-o: #70D6FF;
  --color-game-color-memory-1: var(--color-blue-400);
  --color-game-color-memory-2: var(--color-green-400);
  --color-game-color-memory-3: var(--color-red-400);
  --color-game-color-memory-4: var(--color-yellow-400);
  --color-game-word-search-1: var(--color-red-700);
  --color-game-word-search-2: var(--color-orange-700);
  --color-game-word-search-3: var(--color-amber-700);
  --color-game-word-search-4: var(--color-yellow-700);
  --color-game-word-search-5: var(--color-lime-700);
  --color-game-word-search-6: var(--color-green-700);
  --color-game-word-search-7: var(--color-emerald-700);
  --color-game-word-search-8: var(--color-teal-700);
  --color-game-word-search-9: var(--color-cyan-700);
  --color-game-word-search-10: var(--color-sky-700);
  --color-game-word-search-11: var(--color-blue-700);
  --color-game-word-search-12: var(--color-indigo-700);
  --color-game-word-search-13: var(--color-violet-700);
  --color-game-word-search-14: var(--color-purple-700);
  --color-game-word-search-15: var(--color-fuchsia-700);
  --color-game-word-search-16: var(--color-pink-700);
  --color-game-word-search-17: var(--color-rose-700);
  --color-game-2048-2: var(--color-orange-100);
  --color-game-2048-4: var(--color-orange-200);
  --color-game-2048-8: var(--color-orange-300);
  --color-game-2048-16: var(--color-orange-400);
  --color-game-2048-32: var(--color-red-400);
  --color-game-2048-64: var(--color-red-500);
  --color-game-2048-128: var(--color-yellow-300);
  --color-game-2048-512: var(--color-yellow-400);
  --color-game-2048-4096: var(--color-purple-400);
  --color-game-2048-16384: var(--color-gray-900);
  --color-game-2048-text-below-8: var(--color-gray-700);
  --color-game-2048-text-above-8: var(--color-white);
  --color-game-2048-bg: var(--color-base-300);
  --color-game-sudoku-tile-normal: var(--color-base-100);
  --color-game-sudoku-tile-highlighted: var(--color-gray-700);
  --color-game-sudoku-tile-selected: var(--color-gray-800);
  --color-game-sudoku-tile-immutable: var(--color-gray-600);
  --color-game-sudoku-tile-hint: var(--color-yellow-800);
  --color-game-sudoku-tile-error: var(--color-red-900);
  --color-game-sudoku-tile-border-match: var(--color-gray-400);
  --color-game-sudoku-tile-border-match-selected: var(--color-gray-300);
  --color-game-sudoku-number-normal: var(--color-blue-500);
  --color-game-sudoku-number-hint: var(--color-yellow-400);
  --color-game-sudoku-number-error: var(--color-red-400);
  --color-game-sudoku-check-line: var(--color-red-600);
  --color-game-sudoku-board-inner-lines: var(--color-base-300);
  --color-game-sudoku-board-outer-lines: var(--color-base-300);
  --color-game-minesweeper-grid: var(--color-base-content);
  --color-game-minesweeper-background: var(--color-base-300);
  --color-game-minesweeper-1: #3477F5;
  --color-game-minesweeper-2: #1FBD53;
  --color-game-minesweeper-3: #ED3C3C;
  --color-game-minesweeper-4: #9E4BF6;
  --color-game-minesweeper-5: #F86816;
  --color-game-minesweeper-6: #E7AA0C;
  --color-game-minesweeper-7: #D43DED;
  --color-game-minesweeper-8: #11B076;
  --color-game-minesweeper-highlight: var(--color-yellow-300);
  --color-game-minesweeper-highlight-border: var(--color-yellow-500);
  --color-game-jigsaw-puzzle-stroke: var(--color-base-content);
  --color-game-jigsaw-puzzle-glow: var(--color-yellow-300);

  --confetti-1: var(--color-green-400);
  --confetti-2: var(--color-cyan-400);
  --confetti-3: var(--color-pink-400);
  --confetti-4: var(--color-purple-700);
  --confetti-5: var(--color-teal-400);
  --confetti-6: var(--color-yellow-400);
}

[data-theme="dark-classic"] {
  --color-game-jigsaw-puzzle-stroke: var(--color-white);
}