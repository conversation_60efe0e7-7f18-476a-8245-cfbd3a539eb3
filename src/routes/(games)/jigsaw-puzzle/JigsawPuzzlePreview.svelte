<script lang="ts">
	import { cn } from '$lib/util/cn';
	import { Grid } from '$lib/util/Grid.svelte';
	import Konva from 'konva';
	import type { Node, NodeConfig } from 'konva/lib/Node';
	import { onMount, tick, untrack } from 'svelte';
	import { Layer, Stage } from 'svelte-konva';
	import { type TransitionConfig } from 'svelte/transition';
	import JigsawPiece from './JigsawPiece.svelte';
	import { getImageDimensions } from './util/getImageDimensions';
	import type { Box2D } from '$lib/models/Box2D';
	import type { jigsawPuzzleSoundResources } from './game/jigsawPuzzleSoundResources';
	import type { GameSound } from '$lib/util/GameSound.svelte';

	interface Props {
		src: string;
		rows: number;
		cols: number;
		class?: string;
		transitionDuration?: number;
		noOutTransition?: boolean;
		objectFit?: 'cover' | 'contain';
		seed?: number;
		strokeWidth?: number;
		scale?: number;
		sounds: Pick<
			Record<keyof typeof jigsawPuzzleSoundResources, GameSound>,
			'jigsawPuzzleShuffle' | 'jigsawPuzzleShuffle2'
		>;
	}

	let {
		src,
		rows: propsRows,
		cols: propsCols,
		class: className,
		noOutTransition,
		transitionDuration = 600,
		objectFit = 'contain',
		seed = $bindable(0),
		strokeWidth = 1,
		/** Scale the stage by 1/2 so the stroke is thinner */
		scale = 0.5,
		sounds,
	}: Props = $props();

	let image = $state<HTMLImageElement>();
	let containerElement = $state<HTMLDivElement>();
	let container = $state({ width: 0, height: 0 });
	let stage = $state<{ node: Konva.Node }>();
	let rows = $state(untrack(() => propsRows));
	let cols = $state(untrack(() => propsCols));
	let imageDimensions = $state<Box2D | null>(null);

	// Update dimensions when image or container changes
	$effect(function updateImageDimensions() {
		if (image && container.width > 0 && container.height > 0) {
			imageDimensions = getDimensions();
		}
	});

	// Fit image like css background-contain or background-cover
	function getDimensions(): Box2D | null {
		return getImageDimensions({
			image: {
				width: image?.width ?? 0,
				height: image?.height ?? 0,
			},
			container: {
				width: container.width / scale,
				height: container.height / scale,
			},
			objectFit,
		});
	}

	$effect(function loadImage() {
		if (src) {
			const img = document.createElement('img');
			img.src = src;
			img.onload = () => {
				untrack(() => {
					image = img;
				});
			};
		}
	});

	$effect(() => {
		// Track rows and columns change
		rows;
		cols;

		untrack(() => {
			seed = Math.random() * 100;
		});
	});

	onMount(() => {
		onResize();
	});

	let animationStarted = $state(false);
	let piecesGrid = new Grid<Node<NodeConfig> | undefined>({
		rows: 0,
		columns: 0,
		reactive: false,
	});

	function storeAllPieces() {
		piecesGrid = new Grid<Node<NodeConfig> | undefined>(
			{
				rows,
				columns: cols,
				reactive: false,
			},
			(row, col) => {
				return (stage!.node as Konva.Stage).findOne(`#${row}-${col}`);
			},
		);
	}

	function getStaggeredAnimation({
		fadeIn,
		onComplete,
	}: {
		fadeIn: boolean;
		onComplete?: () => void;
	}) {
		const distance = piecesGrid.rows + piecesGrid.columns - 1;
		const baseDelay = Math.max(0, transitionDuration / distance);

		const animation = new Konva.Animation((frame) => {
			for (let row = 0; row < piecesGrid.rows; row++) {
				for (let col = 0; col < piecesGrid.columns; col++) {
					// Calculate distance from top-left (0,0)
					const distance = row + col;

					// Delay based on distance from top-left
					const delay = distance * baseDelay;
					let progress = frame!.time - delay;

					if (progress < 0) {
						continue;
					}

					if (progress > 1) {
						progress = 1;
					}

					const piece = piecesGrid.grid[row][col];

					const visible = (fadeIn ? progress : 1 - progress) === 1;

					piece?.visible(visible);

					if (progress === 1 && row === piecesGrid.rows - 1 && col === piecesGrid.columns - 1) {
						animation.stop();
						onComplete?.();
					}
				}
			}
		});

		return animation;
	}

	let fadeInAnimation: ReturnType<typeof getStaggeredAnimation> | null = $state(null);
	let fadeOutAnimation: ReturnType<typeof getStaggeredAnimation> | null = $state(null);
	let isFirstAnimation = $state(true);

	$effect(() => {
		if (
			(isFirstAnimation || (propsRows !== rows && propsCols !== cols)) &&
			image &&
			imageDimensions &&
			!fadeInAnimation &&
			!fadeOutAnimation
		) {
			untrack(async () => {
				// wait until pieces are ready (after image dimensions calculation)
				await tick();

				startRevealAnimation();
			});
		}
	});

	async function startRevealAnimation() {
		animationStarted = true;

		if (fadeInAnimation) {
			fadeInAnimation.stop();
		}

		if (fadeOutAnimation) {
			fadeOutAnimation.stop();
		}

		if (isFirstAnimation) {
			storeAllPieces();
			fadeInAnimation = getStaggeredAnimation({
				fadeIn: true,
				onComplete() {
					fadeInAnimation = null;
					isFirstAnimation = false;
				},
			});
			sounds.jigsawPuzzleShuffle.play();
			fadeInAnimation.start();
			return;
		}

		fadeOutAnimation = getStaggeredAnimation({
			fadeIn: false,
			async onComplete() {
				fadeOutAnimation = null;
				rows = propsRows;
				cols = propsCols;

				await tick();

				storeAllPieces();

				fadeInAnimation = getStaggeredAnimation({
					fadeIn: true,
					onComplete() {
						fadeInAnimation = null;
						isFirstAnimation = false;
					},
				});
				sounds.jigsawPuzzleShuffle.play();
				fadeInAnimation.start();
			},
		});

		sounds.jigsawPuzzleShuffle2.play();
		fadeOutAnimation.start();
	}

	function onResize() {
		container = {
			width: containerElement?.clientWidth ?? 0,
			height: containerElement?.clientHeight ?? 0,
		};
	}

	function outTransition(node: HTMLElement): TransitionConfig {
		if (noOutTransition) {
			return {
				duration: 0,
				tick(t, u) {
					return;
				},
			};
		}

		return {
			duration: transitionDuration,
			tick(t, u) {
				if (fadeOutAnimation) {
					return;
				}

				fadeOutAnimation = getStaggeredAnimation({ fadeIn: false });
				sounds.jigsawPuzzleShuffle2.play();
				fadeOutAnimation.start();
			},
		};
	}
</script>

<svelte:window onresize={onResize} />

<div class={cn('size-full', className)} bind:this={containerElement} out:outTransition>
	{#if containerElement}
		<Stage
			width={container.width}
			height={container.height}
			scale={{
				x: scale,
				y: scale,
			}}
			bind:this={stage}
		>
			<Layer opacity={animationStarted ? 1 : 0} listening={false}>
				{#if image && imageDimensions}
					{#each { length: rows } as _, row}
						{#each { length: cols } as _, col}
							<JigsawPiece
								x={imageDimensions.x + (imageDimensions.width / cols) * col}
								y={imageDimensions.y + (imageDimensions.height / rows) * row}
								{row}
								{col}
								{rows}
								{cols}
								{image}
								{seed}
								{container}
								{objectFit}
								{imageDimensions}
								visible={false}
								draggable={false}
								{strokeWidth}
							/>
						{/each}
					{/each}
				{/if}
			</Layer>
		</Stage>
	{/if}
</div>
