import type { GridSize } from '$lib/models/GridSize';
import type { Size } from '$lib/models/Size';
import {
	landscapeGridSizeByPieceAmount,
	squareGridSizeByPieceAmount,
	type PieceSize,
} from '../game/JigsawPuzzleGame.svelte';

const squareErrorMargin = 0.2;

export function getJigsawPuzzleSize(pieceSize: PieceSize, imageDimensions: Size): GridSize {
	const ratio = imageDimensions.width / imageDimensions.height;

	/** Square */
	if (ratio < 1 + squareErrorMargin && ratio > 1 - squareErrorMargin) {
		return squareGridSizeByPieceAmount[pieceSize];
	}

	/** Landscape */
	let size = landscapeGridSizeByPieceAmount[pieceSize];

	/** Portrait */
	if (imageDimensions.width < imageDimensions.height) {
		size = {
			columns: size.rows,
			rows: size.columns,
		};
	}

	return size;
}
