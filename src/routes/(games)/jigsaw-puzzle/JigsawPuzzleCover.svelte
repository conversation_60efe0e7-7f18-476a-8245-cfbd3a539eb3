<script lang="ts">
	import { theme } from '$lib/stores/theme.svelte';

	let url = $derived.by(() => {
		if (theme.loaded) {
			if (theme.brightness === 'light') {
				return 'https://static.lofiandgames.com/covers/jigsaw-puzzle-cover-light.svg';
			} else {
				return 'https://static.lofiandgames.com/covers/jigsaw-puzzle-cover-dark.svg';
			}
		}
	});

	let style = $derived.by(() => {
		if (url) {
			return `background-image: url('${url}')`;
		}
	});
</script>

<div class="p-4 size-full">
	<div class="bg-center bg-cover bg-no-repeat size-full" {style}></div>
</div>
