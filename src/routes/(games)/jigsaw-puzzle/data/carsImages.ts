import type { Wallpaper } from '$lib/data/wallpapers';

export const carsImages: Wallpaper[] = [
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/pexels-peely-712618.avif',
		attribution: {
			work: {
				url: 'https://www.pexels.com/photo/blue-sedan-712618/',
				name: 'Blue Sedan',
			},
			creator: {
				name: 'neil kelly',
				url: 'https://www.pexels.com/@peely/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/pexels-mikebirdy-170811.avif',
		attribution: {
			work: {
				url: 'https://www.pexels.com/photo/blue-bmw-sedan-near-green-lawn-grass-170811/',
				name: 'Blue Bmw Sedan Near Green Lawn Grass',
			},
			creator: {
				name: '<PERSON>',
				url: 'https://www.pexels.com/@mikebirdy/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/pexels-mikebirdy-1335077.avif',
		attribution: {
			work: {
				url: 'https://www.pexels.com/photo/red-mercedes-benz-convertible-1335077/',
				name: 'Red Mercedes-benz Convertible',
			},
			creator: {
				name: 'Mike Bird',
				url: 'https://www.pexels.com/@mikebirdy/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/pexels-mikebirdy-977003.avif',
		attribution: {
			work: {
				url: 'https://www.pexels.com/photo/black-convertible-coupe-977003/',
				name: 'Black Convertible Coupe',
			},
			creator: {
				name: 'Mike Bird',
				url: 'https://www.pexels.com/@mikebirdy/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/pexels-danielspase-951318.avif',
		attribution: {
			work: {
				url: 'https://www.pexels.com/photo/silver-bmw-sedan-951318/',
				name: 'Silver Bmw Sedan',
			},
			creator: {
				name: 'Danila Perevoshchikov',
				url: 'https://www.pexels.com/@danielspase/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/kevin-fitzgerald-Z4CfUB0U9aA-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/white-mercedes-benz-car-on-road-during-daytime-Z4CfUB0U9aA',
				name: 'White Mercedes Benz car on road during daytime',
			},
			creator: {
				name: 'Kevin Fitzgerald',
				url: 'https://unsplash.com/@lespaulster11',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/jakob-owens-U_2kP7bkFKw-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/pink-ford-mustang-U_2kP7bkFKw',
				name: 'Pink Ford Mustang U',
			},
			creator: {
				name: 'Jakob Owens',
				url: 'https://unsplash.com/@jakobowens1',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/philip-schroeder-BNgiIxXXo-A-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/green-car-BNgiIxXXo-A',
				name: '1929 Ford Model A Coupe has been in the family since WWII. Five generations have enjoyed rides in this unique auto. Most recently it carried the fifth generation bride and groom riding away from their wedding venue.',
			},
			creator: {
				name: 'Philip Schroeder',
				url: 'https://unsplash.com/@philinkcmo',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/salim-ben-3TKMYTv0Wpk-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/a-red-sports-car-is-parked-outside-3TKMYTv0Wpk',
				name: 'A red sports car is parked outside',
			},
			creator: {
				name: 'Lance Asper',
				url: 'https://unsplash.com/@lance_asper',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/ryo-ito-YFhEYKPjGOA-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/a-red-sports-car-parked-in-a-parking-lot-YFhEYKPjGOA',
				name: 'A red sports car paked in a parking lot',
			},
			creator: {
				name: 'Ryo Ito',
				url: 'https://unsplash.com/@ryokyleito',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/massimo-p-NHPVHRkJV3M-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/a-yellow-sports-car-parked-in-a-parking-lot-NHPVHRkJV3M',
				name: 'A yellow sports car parked in a parking lot',
			},
			creator: {
				name: 'Massimo P',
				url: 'https://unsplash.com/@max1964',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/kahl-orr-E36kvVnjOBk-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/black-mercedes-benz-coupe-on-road-during-daytime-E36kvVnjOBk',
				name: 'Black Mercedes Benz Coupe on road during daytime',
			},
			creator: {
				name: 'Kahl Orr',
				url: 'https://unsplash.com/@kahlorr',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/volkswagen-4220406_1280.avif',
		attribution: {
			work: {
				url: 'https://pixabay.com/photos/volkswagen-automobile-car-vw-bug-4220406/',
				name: 'Volkswagen, Automobile, Car',
			},
			creator: {
				name: 'ernie114',
				url: 'https://pixabay.com/users/ernie114-7088342/',
			},
		},
	},

	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/adrian-newell-gDtRrlrWh5s-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/red-and-black-lamborghini-aventador-gDtRrlrWh5s',
				name: 'Rose Gold Lamborghini Huracan',
			},
			creator: {
				name: 'Adrian Newell',
				url: 'https://unsplash.com/@anewevisual',
			},
		},
	},
];
