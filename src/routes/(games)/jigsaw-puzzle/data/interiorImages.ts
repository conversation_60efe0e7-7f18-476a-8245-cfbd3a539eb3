import type { Wallpaper } from '$lib/data/wallpapers';

export const interiorImages: Wallpaper[] = [
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/spacejoy-c0JoR_-2x3E-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/a-living-room-filled-with-furniture-and-a-mirror-c0JoR_-2x3E',
				name: 'A living room filled with furniture and a mirror',
			},
			creator: {
				name: 'Spacejoy',
				url: 'https://unsplash.com/@spacejoy',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/kam-idris-_HqHX3LBN18-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/pt-br/fotografias/brown-wooden-framed-yellow-padded-chair-_HqHX3LBN18',
				name: 'Brown wooden framed yellow padded chair',
			},
			creator: {
				name: '<PERSON><PERSON>',
				url: 'https://unsplash.com/pt-br/@ka_idris',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/spacejoy-YqFz7UMm8qE-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/living-room-with-brown-wooden-table-and-chairs-YqFz7UMm8qE',
				name: 'Living room with brown wooden table and chairs',
			},
			creator: {
				name: 'Spacejoy',
				url: 'https://unsplash.com/@spacejoy',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/alexandra-gorn-W5dsm9n6e3g-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/pt-br/fotografias/teal-wooden-drawer-dresser-W5dsm9n6e3g',
				name: 'Teal wooden drawer dresser',
			},
			creator: {
				name: 'Alexandra Gorn',
				url: 'https://unsplash.com/pt-br/@alexagorn',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/alyssa-strohmann-2r2RUsEU1Aw-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/green-cactus-plant-in-room-2r2RUsEU1Aw',
				name: 'Green cactus plant in room',
			},
			creator: {
				name: 'Alyssa Strohmann',
				url: 'https://unsplash.com/@anotherlovely',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/spacejoy-KSfe2Z4REEM-unsplash.avif',
		attribution: {
			work: {
				url: 'https://unsplash.com/photos/brown-wooden-framed-white-padded-chairs-KSfe2Z4REEM',
				name: 'Brown wooden framed white padded chairs',
			},
			creator: {
				name: 'Spacejoy',
				url: 'https://unsplash.com/@spacejoy',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/home-2486092_1280.avif',
		attribution: {
			work: {
				url: 'https://pixabay.com/photos/home-interiors-kitchen-2486092/',
				name: 'Home, Interiors, Kitchen',
			},
			creator: {
				name: 'user32212',
				url: 'https://pixabay.com/users/user32212-763448/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/chairs-2181947_1280.avif',
		attribution: {
			work: {
				url: 'https://pixabay.com/photos/chairs-floor-furniture-indoors-2181947/',
				name: 'Chairs, Floor, Furniture',
			},
			creator: {
				name: 'Pexels',
				url: 'https://pixabay.com/users/pexels-2286921/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/pexels-fotoaibe-1571459.avif',
		attribution: {
			work: {
				url: 'https://www.pexels.com/photo/turned-on-gray-flat-screen-smart-tv-1571459/',
				name: 'Turned-on Gray Flat Screen Smart Tv',
			},
			creator: {
				name: 'Vecislavas Popa',
				url: 'https://www.pexels.com/@fotoaibe/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/pexels-houzlook-3356416.avif',
		attribution: {
			work: {
				url: 'https://www.pexels.com/photo/gray-dining-table-under-pendant-lamps-3356416/',
				name: 'Gray Dining Table Under Pendant Lamps',
			},
			creator: {
				name: 'Houzlook .com',
				url: 'https://www.pexels.com/@houzlook/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/images/jigsaw-puzzle/pexels-tove-liu-2127454-3937645.avif',
		attribution: {
			work: {
				url: 'https://www.pexels.com/photo/a-mall-in-glass-covered-arcade-3937645/',
				name: 'A Mall In Glass Covered Arcade',
			},
			creator: {
				name: 'Tove Liu',
				url: 'https://www.pexels.com/@tove-liu-2127454/',
			},
		},
	},
];
