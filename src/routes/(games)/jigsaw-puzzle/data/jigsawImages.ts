import { ghibliDarkWallpapers, ghibliLightWallpapers, type Wallpaper } from '$lib/data/wallpapers';
import { animalsImages } from './animalsImages';
import { cityImages } from './cityImages';
import type { JigsawCategory } from './jigsawCategories';
import { landscapeImages } from './landscapeImages';
import { getJigsawId } from '../util/getJigsawId';
import { interiorImages } from './interiorImages';
import { technologyImages } from './technologyImages';

export interface JigsawImage extends Wallpaper {
	id: string;
}

export type JigsawImages = {
	images: JigsawImage[];
	category: JigsawCategory;
};

export const jigsawImages: JigsawImages[] = [
	{
		category: 'ghibli',
		images: [...ghibliDarkWallpapers, ...ghibliLightWallpapers],
	},
	{
		category: 'landscape',
		images: landscapeImages,
	},
	{
		category: 'animals',
		images: animalsImages,
	},
	{
		category: 'city-and-architecture',
		images: cityImages,
	},
	{
		category: 'interior',
		images: interiorImages,
	},
	{
		category: 'technology',
		images: technologyImages,
	},
].sort((a, b) => {
	return a.category.localeCompare(b.category);
}) as JigsawImages[];

// Assign ids to all images
jigsawImages.forEach(({ images, category }) => {
	images.forEach((image) => {
		image.id = getJigsawId({ image, category });
	});
});

const allImages = [...jigsawImages.flatMap((img) => img.images)];

const first = allImages[0];
const second = allImages[1];

// Swap first and second so it does not show the same image as the first
allImages[0] = second;
allImages[1] = first;

jigsawImages.unshift({
	category: 'all',
	images: allImages,
});
