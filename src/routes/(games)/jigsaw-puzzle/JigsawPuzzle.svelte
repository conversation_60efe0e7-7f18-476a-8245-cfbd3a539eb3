<script lang="ts">
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import { onDestroy, onMount, untrack } from 'svelte';
	import { JigsawPuzzleGame, pieceSizes } from './game/JigsawPuzzleGame.svelte';
	import JigsawPuzzleSetup from './JigsawPuzzleSetup.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import type { PieceSize } from './game/JigsawPuzzleGame.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import { wait } from '$lib/functions/wait';
	import { type Size } from '$lib/models/Size';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import { jigsawPuzzleSoundResources } from './game/jigsawPuzzleSoundResources';
	import JigsawPuzzleInfoModal from './JigsawPuzzleInfoModal.svelte';
	import { getJigsawPuzzleSize } from './util/getJigsawPuzzleSize';
	import { getImageDimensions } from './util/getImageDimensions';
	import { formatJigsawPuzzleSize } from './util/formatJigsawPuzzleSize';
	import { isBrave } from '$lib/functions/isBrave';
	import { toast } from 'svelte-sonner';
	import type { JigsawImage } from './data/jigsawImages';
	import type { JigsawCategory } from './data/jigsawCategories';
	import { pushState } from '$app/navigation';
	import FeedbackModal from '$lib/components/FeedbackModal.svelte';
	import FeedbackIcon from '$lib/components/Icons/FeedbackIcon.svelte';
	import { authClient } from '$lib/auth/client';

	export interface StartProps {
		image: JigsawImage;
		category?: JigsawCategory;
		seed: number;
		container: Size;
		originalImageSize: Size;
		animate?: boolean;
		pieceSize?: PieceSize;
	}

	interface Props {
		selectedImage?: JigsawImage;
		selectedCategory?: JigsawCategory;
		autoStart?: StartProps;
	}

	let { selectedImage = $bindable(), selectedCategory = $bindable(), autoStart }: Props = $props();

	let image = $state(selectedImage);
	let imageSize = $state<Size>();
	let seed = $state(0);
	let container = $state<Size>({ width: 0, height: 0 });
	let gameIsland: GameIsland<any, any, any> | null = $state(null);
	let gameContainer: HTMLDivElement | null = $state(null);
	let isInfoModalOpen = $state(false);

	type Step = 'setup' | 'game';
	let step = $state<Step>('setup');

	type TransitionState = 'none' | 'in' | 'out';
	let transitionState = $state<TransitionState>('none');
	const transitionDuration = 1000;
	const transitionOutDelay = 500;

	const context = new GameContext({
		GameClass: JigsawPuzzleGame,
		gameKey: 'jigsaw-puzzle',
		settings: {
			defaultSettings: {
				pieces: pieceSizes[1] as PieceSize,
			},
		},
		sounds: {
			resources: jigsawPuzzleSoundResources,
			lifecycle: {
				win: jigsawPuzzleSoundResources.gameWin,
			},
		},
		defaultGameProps(context) {
			return {
				seed: 0,
				image: selectedImage ?? { url: '', id: '' },
				container: { width: 0, height: 0 },
				pieceSize: context.settingsManager.settings.pieces,
				timer: context.timer,
				target: gameContainer || document.body,
				sounds: context.sounds,
				onGameWin: () => {
					context.handleGameOver('won');
				},
			};
		},
		formatted(context) {
			return {
				name: 'Jigsaw Puzzle',
				variant: `${formatJigsawPuzzleSize(context.game?.pieceSize ?? context.settingsManager.settings.pieces)}`,
			};
		},
		stats({ props, context }) {
			return {
				stats: new Stats({
					...props,
					gameVariant: context.settingsManager.settings.pieces,
					liveStats: {
						piecesInPlace: {
							name: 'Pieces in Place',
							unit: 'plain',
							value: () => context.game?.piecesInPlace ?? 0,
						},
						missingPieces: {
							name: 'Missing Pieces',
							unit: 'plain',
							value: () => {
								const size =
									(context.game?.grid.size.rows ?? 0) * (context.game?.grid.size.columns ?? 0);

								return size - (context.game?.piecesInPlace ?? 0);
							},
						},
					},
					initialPinnedStats: ['time', 'piecesInPlace'],
				}),
				canUpdateWithGameLost: () => false,
				visibleStats: ['bestTime', 'averageTime', 'wonGames', 'totalGames'],
			};
		},
		onDispose(context) {
			context.game?.dispose();
		},
		onWillCreateGame({ previousGame }) {
			previousGame?.dispose();
		},
	});

	async function onStart(startProps: StartProps) {
		const animate = startProps.animate ?? true;

		image = startProps.image;
		seed = startProps.seed;
		container = startProps.container;
		imageSize = startProps.originalImageSize;
		selectedCategory = startProps.category;

		if (animate) {
			transitionState = 'in';

			await wait(transitionDuration);
		}

		context.createGame({
			pieceSize: startProps.pieceSize ?? context.settingsManager.settings.pieces,
			image,
			seed,
			container,
		});

		step = 'game';

		if (animate) {
			await wait(transitionOutDelay);

			transitionState = 'out';

			await wait(transitionDuration);

			transitionState = 'none';
		}

		context.timer.start();
	}

	async function backToSetup() {
		if (context.game?.imageDimensions) {
			container = {
				width: gameContainer?.clientWidth ?? context.game.imageDimensions.width,
				height: gameContainer?.clientHeight ?? context.game.imageDimensions.height,
			};
			imageSize = context.game.imageDimensions;
		}
		selectedImage = undefined;

		if (selectedCategory) {
			pushState(`/jigsaw-puzzle/${selectedCategory}`, {});
		} else {
			pushState(`/jigsaw-puzzle`, {});
		}

		transitionState = 'in';

		await wait(transitionDuration);

		step = 'setup';

		await wait(transitionOutDelay);

		transitionState = 'out';

		await wait(transitionDuration);

		transitionState = 'none';

		seed = 0;
		// image = undefined;
	}

	async function onLeaveGame({ gameLost }: { gameLost?: boolean }) {
		if (gameLost) {
			context.handleGameOver('lost');
		}
		context.game?.dispose();
		context.resetGameState();
		gameIsland?.changeVariant('live-stats');

		backToSetup();
	}

	let isMounted = $state(false);
	let JigsawPuzzlePreview = $state<typeof import('./JigsawPuzzlePreview.svelte').default>();
	let JigsawPuzzleGameRenderer =
		$state<typeof import('./JigsawPuzzleGameRenderer.svelte').default>();

	onMount(async () => {
		context.load();

		JigsawPuzzlePreview = (await import('./JigsawPuzzlePreview.svelte')).default;
		JigsawPuzzleGameRenderer = (await import('./JigsawPuzzleGameRenderer.svelte')).default;
		isMounted = true;

		if (await isBrave()) {
			toast('Browser Issue', {
				id: 'jigsaw-puzzle-brave-toast',
				dismissable: true,
				description:
					'The Brave Shield protection prevents the Jigsaw Puzzle game from working properly. Please disable it to play the game correctly',
				duration: Number.POSITIVE_INFINITY,
				action: {
					label: 'Got it!',
					onClick() {
						toast.dismiss('jigsaw-puzzle-brave-toast');
					},
				},
			});
		}
	});

	onDestroy(() => {
		context.dispose();
	});

	let isFeedbackOpen = $state(false);
	let session = authClient.useSession();

	$effect(function handleAutoStart() {
		if (
			isMounted &&
			autoStart &&
			JigsawPuzzleGameRenderer &&
			JigsawPuzzlePreview &&
			context.isLoaded
		) {
			untrack(() => {
				onStart(autoStart);
			});
		}
	});
</script>

<JigsawPuzzleInfoModal bind:isOpen={isInfoModalOpen} />

<FeedbackModal context="Jigsaw Puzzle" bind:isOpen={isFeedbackOpen} />

<button
	class="btn btn-circle btn-sm absolute bottom-4 right-4 z-10"
	onclick={() => (isFeedbackOpen = true)}
>
	<FeedbackIcon class="size-6"></FeedbackIcon>
</button>

<GameLayout
	noPadding
	navbarStyle={context.timer.started ? 'on-top-as-glass' : 'on-top'}
	navbarProps={{
		profileButtonProps: {
			extraMenuItems: [
				{
					Icon: InfoSolidIcon,
					title: 'About this game',
					onclick: () => (isInfoModalOpen = true),
				},
			],
		},
	}}
>
	{#snippet Island()}
		<GameIsland
			{context}
			bind:this={gameIsland}
			onLeaveGame={(url) => {
				if (!url) {
					// User left by interacting with the back button, change step to setup
					onLeaveGame({ gameLost: true });
				}
			}}
			onNewGame={() => {
				onLeaveGame({ gameLost: false });
			}}
			isClosedBeta
		/>
	{/snippet}

	{#if $session.data?.user}
		<div class="h-screen w-screen overflow-clip z-0" bind:this={gameContainer}>
			{#if step === 'setup'}
				<JigsawPuzzleSetup
					bind:pieceSize={context.settingsManager.settings.pieces}
					{onStart}
					sounds={context.sounds}
					bind:selectedImage
					bind:selectedCategory
				/>
			{:else if step === 'game' && context.game}
				<JigsawPuzzleGameRenderer
					game={context.game}
					onBack={() => {
						gameIsland?.changeVariant('prevent-leave');
					}}
				/>
			{/if}

			{#if JigsawPuzzlePreview && transitionState === 'in' && image && imageSize && container}
				{@const { rows, columns: cols } = getJigsawPuzzleSize(
					'xl',
					getImageDimensions({
						container,
						image: imageSize,
						objectFit: 'cover',
					})!,
				)}
				<JigsawPuzzlePreview
					objectFit="cover"
					{rows}
					{cols}
					src={image.url}
					class="fixed inset-0 z-10"
					transitionDuration={transitionDuration - 200}
					scale={1}
					strokeWidth={1}
					sounds={context.sounds}
				/>
			{/if}
		</div>
	{/if}
</GameLayout>
