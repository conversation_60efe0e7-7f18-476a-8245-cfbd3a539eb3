import { HitBox, type Rect, type Side } from '$lib/util/HitBox';
import { Easing, Tween } from '@tweenjs/tween.js';
import type { CompleteAnimationEntity } from '../../solitaire/systems/AnimationSystem';
import type { JigsawPuzzlePieceGroup } from './JigsawPuzzlePieceGroup.svelte';

interface JigsawPuzzlePieceProps {
	x?: number;
	y?: number;
	column: number;
	row: number;
	width: number;
	height: number;
}

const hitboxSizeRatio = 1 / 3;
export const hitboxCenterRatio = 1 / 3;
export const pieceMoveDuration = 180;

type HitBoxes = Record<Side | 'center', HitBox>;

export class JigsawPuzzlePiece {
	x = $state(0);
	y = $state(0);
	column = $state(0);
	row = $state(0);
	width = $state(0);
	height = $state(0);
	private _isInPlace = $state(false);
	group = $state<JigsawPuzzlePieceGroup | null>(null);
	isGrouped = $derived(this.group !== null);
	/** HitBoxes for each edge of the piece. Assumes a top-left anchor */
	hitboxes!: HitBoxes;
	glow = $state(false);

	constructor({ x, y, column, row, width, height }: JigsawPuzzlePieceProps) {
		this.x = x ?? 0;
		this.y = y ?? 0;
		this.column = column;
		this.row = row;
		this.width = width;
		this.height = height;

		this.initHitboxes();
	}

	get isInPlace() {
		return this._isInPlace;
	}

	set isInPlace(value: boolean) {
		this._isInPlace = value;

		if (value) {
			this.group?.remove(this);
		}
	}

	get globalX() {
		return this.x + (this.group?.x ?? 0);
	}

	get globalY() {
		return this.y + (this.group?.y ?? 0);
	}

	/**
	 * Init 4 hitboxes on the edges of the piece
	 */
	private initHitboxes() {
		const hitboxWidth = this.width * hitboxSizeRatio;
		const hitboxHeight = this.height * hitboxSizeRatio;

		this.hitboxes = {
			center: new HitBox({
				x: ((1 - hitboxCenterRatio) * this.width) / 2,
				y: ((1 - hitboxCenterRatio) * this.height) / 2,
				w: this.width * hitboxCenterRatio,
				h: this.height * hitboxCenterRatio,
			}),
			left: new HitBox({
				x: -hitboxWidth / 2,
				y: (this.height - hitboxHeight) / 2,
				w: hitboxWidth,
				h: hitboxHeight,
			}),
			right: new HitBox({
				x: this.width - hitboxWidth / 2,
				y: (this.height - hitboxHeight) / 2,
				w: hitboxWidth,
				h: hitboxHeight,
			}),
			top: new HitBox({
				x: (this.width - hitboxWidth) / 2,
				y: -hitboxHeight / 2,
				w: hitboxWidth,
				h: hitboxHeight,
			}),
			bottom: new HitBox({
				x: (this.width - hitboxWidth) / 2,
				y: this.height - hitboxHeight / 2,
				w: hitboxWidth,
				h: hitboxHeight,
			}),
		};

		this.updateHitboxesPosition();
	}

	private updateHitboxesPosition() {
		const globalPos = { x: this.x + (this.group?.x ?? 0), y: this.y + (this.group?.y ?? 0) };

		this.hitboxes.center.position = globalPos;
		this.hitboxes.left.position = globalPos;
		this.hitboxes.right.position = globalPos;
		this.hitboxes.top.position = globalPos;
		this.hitboxes.bottom.position = globalPos;
	}

	collidesWith(rect: Rect) {
		// Update hitboxes positions, then check collision
		this.updateHitboxesPosition();

		return this.hitboxes.center.collidesWith(rect);
	}

	match(other: JigsawPuzzlePiece): boolean {
		// Update hitboxes positions, then check collision
		this.updateHitboxesPosition();
		other.updateHitboxesPosition();

		if (this.row === other.row) {
			// This instance is left, other is at right
			if (this.column === other.column - 1) {
				return this.hitboxes.right.collidesWith(other.hitboxes.left);
			}

			// This instance is right, other is at left
			if (this.column === other.column + 1) {
				return this.hitboxes.left.collidesWith(other.hitboxes.right);
			}
		}

		if (this.column === other.column) {
			// This instance is top, other is at bottom
			if (this.row === other.row - 1) {
				return this.hitboxes.bottom.collidesWith(other.hitboxes.top);
			}

			// This instance is bottomm other is at top
			if (this.row === other.row + 1) {
				return this.hitboxes.top.collidesWith(other.hitboxes.bottom);
			}
		}

		return false;
	}

	moveTo({
		x,
		y,
		duration = pieceMoveDuration,
		onComplete,
	}: {
		x: number;
		y: number;
		duration?: number;
		onComplete?: () => void;
	}): CompleteAnimationEntity<Tween> | null {
		if (Math.abs(this.x - x) < 0.01 && Math.abs(this.y - y) < 0.01) {
			return null;
		}

		const animation = new Tween({
			x: this.x,
			y: this.y,
		})
			.to({ x, y })
			.duration(duration)
			.easing(Easing.Quadratic.InOut)
			.onUpdate(({ x, y }) => {
				this.x = x;
				this.y = y;
			})
			.onComplete(onComplete);

		return {
			id: `move-${this.row}-${this.column}`,
			animation,
		};
	}
}
