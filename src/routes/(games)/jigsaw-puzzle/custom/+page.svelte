<script lang="ts">
	import { MetaTags } from 'svelte-meta-tags';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { pieceSizes, type PieceSize } from '../game/JigsawPuzzleGame.svelte';
	import { goto } from '$app/navigation';
	import JigsawPuzzle, { type StartProps } from '../JigsawPuzzle.svelte';
	import { toast } from 'svelte-sonner';
	import { onMount } from 'svelte';

	let pieceSize = $state<PieceSize>();
	let imageUrl = $state<string>();
	let imageElement = $state<HTMLImageElement>();
	let containerElement: HTMLDivElement;
	let autoStart = $state<StartProps>();

	let searchParams = $state<URLSearchParams>();

	onMount(async () => {
		searchParams = new URLSearchParams(window.location.search);

		const linkParam = searchParams.get('link');
		const sizeParam = searchParams.get('size');

		if (pieceSizes.includes(sizeParam as any)) {
			pieceSize = sizeParam as any;
		} else {
			pieceSize = pieceSizes[1];
		}

		if (linkParam) {
			try {
				imageUrl = decodeURIComponent(linkParam);
			} catch (_) {
				handleError();
			}
		} else {
			handleError();
		}
	});

	function handleError() {
		setTimeout(() => {
			toast.error('Oops, there was an error loading this puzzle', {
				duration: 5_000,
				id: 'error-loading-puzzle',
				dismissable: true,
			});
		}, 500);

		goto('/jigsaw-puzzle');
	}

	async function handleImageLoad() {
		if (!imageUrl || !imageElement) {
			return;
		}

		autoStart = {
			container: {
				width: containerElement.clientWidth ?? 0,
				height: containerElement.clientHeight ?? 0,
			},
			image: {
				id: encodeURIComponent(imageUrl),
				url: imageUrl,
			},
			originalImageSize: {
				width: imageElement.naturalWidth ?? 0,
				height: imageElement.naturalHeight ?? 0,
			},
			seed: 0,
			animate: true,
			pieceSize,
		};
	}
</script>

<MetaTags
	title="Custom Puzzle | Play Jigsaw Puzzle Online for Free"
	titleTemplate="%s | Lofi and Games"
	description="Play jigsaw puzzle online for free. Beautiful jigsaw puzzle game, delightful gaming experience, no download nor registration is required."
	canonical="https://www.lofiandgames.com/jigsaw-puzzle"
	openGraph={{
		url: 'https://www.lofiandgames.com/jigsaw-puzzle',
		images: [
			{
				url: 'https://www.lofiandgames.com/share-jigsaw-puzzle.png',
				width: 1200,
				height: 630,
				alt: 'Jigsaw Puzzle Game',
			},
		],
		siteName: 'Lofi and Games',
		type: 'game',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Play Jigsaw Puzzle on Lofi and Games',
		image: 'https://www.lofiandgames.com/share-jigsaw-puzzle.png',
		site: 'https://www.lofiandgames.com/jigsaw-puzzle',
	}}
/>

<PageTransition>
	<div class="absolute -top-96 invisible aspect-video w-80" bind:this={containerElement}>
		{#if imageUrl}
			<img
				src={imageUrl}
				bind:this={imageElement}
				alt=""
				class="invisible w-full"
				onload={handleImageLoad}
				onerror={handleError}
			/>
		{/if}
	</div>

	{#if autoStart}
		<JigsawPuzzle {autoStart} />
	{/if}
</PageTransition>
