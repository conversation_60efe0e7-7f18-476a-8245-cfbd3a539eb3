<script lang="ts">
	import { MetaTags } from 'svelte-meta-tags';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { page } from '$app/state';
	import { pieceSizes, type PieceSize } from '../../../game/JigsawPuzzleGame.svelte';
	import { replaceState } from '$app/navigation';
	import JigsawPuzzle, { type StartProps } from '../../../JigsawPuzzle.svelte';
	import { toast } from 'svelte-sonner';
	import { wait } from '$lib/functions/wait';

	let pieceSize = page.params.pieceSize as PieceSize;
	let imageUrl = decodeURIComponent(page.params.imageUrl);
	let imageElement: HTMLImageElement;
	let containerElement: HTMLDivElement;
	let autoStart = $state<StartProps>();

	async function handleImageLoad() {
		if (!pieceSizes.includes(pieceSize as any)) {
			pieceSize = pieceSizes[1];
			replaceState(`/jigsaw-puzzle/custom/${encodeURIComponent(imageUrl)}/${pieceSize}`, {});
		}

		await wait(400);

		autoStart = {
			container: {
				width: containerElement.clientWidth ?? 0,
				height: containerElement.clientHeight ?? 0,
			},
			image: {
				id: encodeURIComponent(imageUrl),
				url: imageUrl,
			},
			originalImageSize: {
				width: imageElement.naturalWidth ?? 0,
				height: imageElement.naturalHeight ?? 0,
			},
			seed: 0,
			animate: true,
			pieceSize,
		};
	}
</script>

<MetaTags
	title="Custom Puzzle | Play Jigsaw Puzzle Online for Free"
	titleTemplate="%s | Lofi and Games"
	description="Play jigsaw puzzle online for free. Beautiful jigsaw puzzle game, delightful gaming experience, no download nor registration is required."
	canonical="https://www.lofiandgames.com/jigsaw-puzzle"
	openGraph={{
		url: 'https://www.lofiandgames.com/jigsaw-puzzle',
		images: [
			{
				url: 'https://www.lofiandgames.com/share-jigsaw-puzzle.png',
				width: 1200,
				height: 630,
				alt: 'Jigsaw Puzzle Game',
			},
		],
		siteName: 'Lofi and Games',
		type: 'game',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Play Jigsaw Puzzle on Lofi and Games',
		image: 'https://www.lofiandgames.com/share-jigsaw-puzzle.png',
		site: 'https://www.lofiandgames.com/jigsaw-puzzle',
	}}
/>

<PageTransition>
	<div class="absolute -top-96 invisible aspect-video w-80" bind:this={containerElement}>
		<img
			src={imageUrl}
			bind:this={imageElement}
			alt=""
			class="invisible w-full"
			onload={handleImageLoad}
			onerror={() => {
				replaceState('/jigsaw-puzzle', {});
				toast.error('Oops, there was an error loading this puzzle', {
					duration: 5_000,
					id: 'error-loading-puzzle',
					dismissable: true,
				});
			}}
		/>
	</div>

	{#if autoStart}
		<JigsawPuzzle {autoStart} />
	{/if}
</PageTransition>
