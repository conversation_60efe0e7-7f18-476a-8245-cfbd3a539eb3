<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { onDestroy, onMount, untrack } from 'svelte';
	import {
		tentsDifficulties,
		TentsGame,
		TentsItem,
		type TentsDifficulty,
	} from './TentsGame.svelte';
	import { TentsSoundResources } from './TentsSoundResources';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import type { GridSize } from '$lib/models/GridSize';
	import TentsHowToPlayButton from './TentsHowToPlayButton.svelte';
	import TentsGameRenderer from './TentsGameRenderer.svelte';
	import { UndoableKeyboardListener } from '$lib/util/Undoable/UndoableKeyboardListener';
	import UndoIcon from '$lib/components/Icons/UndoIcon.svelte';
	import RedoIcon from '$lib/components/Icons/RedoIcon.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import capitalize from 'lodash/capitalize';
	import { MediaQuery } from 'svelte/reactivity';
	import { pickRandom, Smush32 } from '@thi.ng/random';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import Collapse from '$lib/components/Collapse/Collapse.svelte';
	import CollapseContent from '$lib/components/Collapse/CollapseContent.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import TentsInfoModal from './TentsInfoModal.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';

	let isInfoModalOpen = $state(false);

	let isSizeDropdownOpen = $state(false);
	let isDifficultyDropdownOpen = $state(false);
	let undoableListener: UndoableKeyboardListener;
	let hasPlacedAnyItemSincePointerDown = false;
	let totalPointerDown = 0;
	const large = new MediaQuery('min-width: 1024px');

	const context = new GameContext({
		gameKey: 'tents',
		GameClass: TentsGame,
		sounds: {
			resources: TentsSoundResources,
			lifecycle: {
				createGame: TentsSoundResources.replay,
				win: TentsSoundResources.gameWin,
			},
		},
		settings: {
			defaultSettings: {
				difficulty: 'easy' as TentsDifficulty,
				size: {
					rows: 6,
					columns: 6,
				} as GridSize,
				autoFillZeros: false,
				autoFillNotAdjacentToTrees: false,
				autoFillTentsAmountReached: false,
				autoFillAroundTents: false,
				autoFill: false,
			},
		},
		variants: {
			difficulty: {
				allValues: tentsDifficulties,
				fromGame(game) {
					return game.difficulty;
				},
				format: capitalize,
			},
			size: {
				allValues: [
					{
						rows: 6,
						columns: 6,
					},
					{
						rows: 8,
						columns: 8,
					},
					{
						rows: 10,
						columns: 10,
					},
					{
						rows: 12,
						columns: 12,
					},
					{
						rows: 15,
						columns: 15,
					},
				],
				fromGame(game) {
					return game.board.size;
				},
				format(size) {
					return `${size.rows}x${size.columns}`;
				},
			},
		},
		formatted(context) {
			const variant = `${context.variants!.size?.format(context.game?.board.size ?? { rows: 6, columns: 6 })} - ${context.variants!.difficulty?.format	(context.game?.difficulty ?? 'easy')}`;

			return {
				name: 'Tents',
				variant,
				leaderboardVariant: variant,
				dailyVariant: context.dailyGame?.gameVariant ? 'Mobile' : undefined,
			};
		},
		defaultGameProps(context) {
			const settings = context.settingsManager.settings;

			return {
				difficulty: settings.difficulty,
				size: settings.size,
				runtimeSettings: {
					autoFillNotAdjacentToTrees: settings.autoFillNotAdjacentToTrees,
					autoFillTentsAmountReached: settings.autoFillTentsAmountReached,
					autoFillZeros: settings.autoFillZeros,
					autoFillAroundTents: settings.autoFillAroundTents,
					autoFill: settings.autoFill,
				},
			};
		},
		stats({ props, context }) {
			const game = context.game!;
			const gameVariant = `${game.board.rows ?? 6}x${game.board.columns ?? 6},${game.difficulty}`;

			return {
				stats: new Stats({
					...props,
					gameVariant,
					liveStats: {
						placedTents: {
							name: 'Placed Tents',
							unit: 'plain',
							value() {
								return game.itemsAmounts.tents;
							},
						},
						tentsToPlace: {
							name: 'Tents to Place',
							unit: 'plain',
							value() {
								return Math.max(0, game.itemsAmounts.trees - game.itemsAmounts.tents);
							},
						},
					},
					initialPinnedStats: ['time', 'placedTents'],
				}),
				visibleStats: ['bestTime', 'averageTime', 'wonGames', 'totalGames'],
				canUpdateWithGameLost(game) {
					return !game.isWon && totalPointerDown > 5;
				},
			};
		},
		leaderboard(context) {
			return {
				leaderboard: new Leaderboard({
					firstAvailableDate: new Date('2025/05/08'),
					game: context.gameKey,
					gameVariant: `${context.game?.board.rows ?? 6}x${context.game?.board.columns ?? 6}-${context.game?.difficulty}`,
					order: 'lower-first',
				}),
				sendScoreOn: ['won'],
				getScore(game) {
					return {
						score: game.getScore(context.timer.elapsedTime),
					};
				},
			};
		},
		dailyGame() {
			return {
				type: 'seed',
				gameVariant: large.current ? undefined : 'Mobile',
				firstAvailableGameDate: new Date('2025/01/11'),
				toProps(seed: number) {
					const random = new Smush32(seed);
					const sizes = large.current ? [10, 12, 15] : [8, 10];
					const size = pickRandom(sizes, random);

					return {
						difficulty: 'hard' as TentsDifficulty,
						size: {
							rows: size,
							columns: size,
						},
						seed,
					};
				},
			};
		},
		onWillCreateGame({ newGameOptions, context }) {
			if (!newGameOptions.isDaily) {
				context.settingsManager.settings.difficulty = newGameOptions.difficulty;
				context.settingsManager.settings.size = newGameOptions.size;
			}
		},
		onGameCreated() {
			totalPointerDown = 0;
			isSizeDropdownOpen = false;
			isDifficultyDropdownOpen = false;
		},
	});

	let game = $derived.by(() => {
		if (context.game) {
			return context.game;
		}

		return untrack(
			() => new TentsGame({ empty: true, difficulty: 'easy', size: { columns: 0, rows: 0 } }),
		);
	});

	$effect(function syncSettings() {
		const {
			autoFillNotAdjacentToTrees,
			autoFillTentsAmountReached,
			autoFillZeros,
			autoFillAroundTents,
			autoFill,
		} = context.settingsManager.settings;

		untrack(() => {
			game.runtimeSettings = {
				autoFillNotAdjacentToTrees,
				autoFillTentsAmountReached,
				autoFillZeros,
				autoFillAroundTents,
				autoFill,
			};
		});
	});

	async function checkGameWin() {
		if (game.isWon) {
			context.handleGameOver('won');
		}
	}

	function startOver() {
		context.sounds?.replay?.play();
		game.startOver();
		isSizeDropdownOpen = false;
		isDifficultyDropdownOpen = false;
		context.timer.reset();
	}

	function undo() {
		game.undo();
	}

	function redo() {
		game.redo();
	}

	onMount(() => {
		undoableListener = new UndoableKeyboardListener(undo, redo);
		undoableListener.listen();
		context.load();
	});

	onDestroy(() => {
		context.dispose();

		if (undoableListener) {
			undoableListener.dispose();
		}
	});

	let isTouching = false;

	function handlePointerDown(event: PointerEvent) {
		if (game.isWon) {
			return;
		}

		isTouching = true;

		const element = event.target as HTMLElement;
		const { row, column } = element.dataset;

		if (row !== undefined && column !== undefined) {
			if (!context.timer.running) {
				context.timer.start();
			}

			const placedItem = game.placeExpectedItem({ row: +row, column: +column });

			checkGameWin();

			if (placedItem.item === TentsItem.Tent) {
				context.sounds.placeTent.play();
			} else if (placedItem.previousItem === TentsItem.Tent) {
				context.sounds.removeTent.play();
			}

			if (placedItem.success) {
				hasPlacedAnyItemSincePointerDown = true;
				totalPointerDown += 1;
			}
		}
	}

	function handlePointerMove(event: PointerEvent) {
		if (!isTouching || game.isWon) {
			return;
		}
		const element = event.target as HTMLElement;
		const { row, column } = element.dataset;

		if (row !== undefined && column !== undefined) {
			if (game.continuePlacing({ row: +row, column: +column })) {
				checkGameWin();
				hasPlacedAnyItemSincePointerDown = true;
			}
		}
		event.preventDefault();
	}

	function handlePointerUp() {
		if (hasPlacedAnyItemSincePointerDown) {
			game.commitToHistory();
		}
		hasPlacedAnyItemSincePointerDown = false;
		isTouching = false;
	}
</script>

<svelte:window onpointerup={handlePointerUp} onpointerdown={handlePointerDown} />

<GameLayout noPadding>
	{#snippet Island()}
		<GameIsland {context} />
	{/snippet}

	<div
		class={twMerge(
			'max-h-screen-no-navbar w-full relative',
			game.board.rows >= 15 ? 'max-w-2xl' : 'max-w-xl',
		)}
	>
		<div class="flex items-center gap-2 px-4">
			<TentsHowToPlayButton />

			<Dropdown bind:open={isSizeDropdownOpen}>
				<DropdownButton class="btn-sm">
					{game.board.rows}x{game.board.columns}
				</DropdownButton>

				<DropdownContent menu>
					<DropdownItem>
						<button
							class:menu-active={game.board.rows === 6}
							onclick={() =>
								context.createGame({ difficulty: game.difficulty, size: { rows: 6, columns: 6 } })}
						>
							6x6
						</button>
					</DropdownItem>
					<DropdownItem>
						<button
							class:menu-active={game.board.rows === 8}
							onclick={() =>
								context.createGame({ difficulty: game.difficulty, size: { rows: 8, columns: 8 } })}
						>
							8x8
						</button>
					</DropdownItem>
					<DropdownItem>
						<button
							class:menu-active={game.board.rows === 10}
							onclick={() =>
								context.createGame({
									difficulty: game.difficulty,
									size: { rows: 10, columns: 10 },
								})}
						>
							10x10
						</button>
					</DropdownItem>
					<DropdownItem>
						<button
							class:menu-active={game.board.rows === 12}
							class="hidden md:block"
							onclick={() =>
								context.createGame({
									difficulty: game.difficulty,
									size: { rows: 12, columns: 12 },
								})}
						>
							12x12
						</button>
					</DropdownItem>
					<DropdownItem>
						<button
							class:menu-active={game.board.rows === 15}
							class="hidden lg:block"
							onclick={() =>
								context.createGame({
									difficulty: game.difficulty,
									size: { rows: 15, columns: 15 },
								})}
						>
							15x15
						</button>
					</DropdownItem>
				</DropdownContent>
			</Dropdown>

			<Dropdown bind:open={isDifficultyDropdownOpen}>
				<DropdownButton class="btn-sm">
					{game.difficulty === 'easy' ? 'Easy' : 'Hard'}
				</DropdownButton>

				<DropdownContent menu>
					<DropdownItem>
						<button
							class:menu-active={game.difficulty === 'easy'}
							onclick={() => context.createGame({ difficulty: 'easy', size: game.board.size })}
						>
							Easy
						</button>
					</DropdownItem>
					<DropdownItem>
						<button
							class:menu-active={game.difficulty === 'hard'}
							onclick={() => context.createGame({ difficulty: 'hard', size: game.board.size })}
						>
							Hard
						</button>
					</DropdownItem>
				</DropdownContent>
			</Dropdown>

			<button class="btn btn-sm" onclick={() => startOver()}>Start over</button>

			{@render Settings('hidden sm:block dropdown-end')}

			<button class="btn btn-sm hidden sm:flex" onclick={() => (isInfoModalOpen = true)}>
				<InfoSolidIcon class="size-5" />
			</button>

			<MoreGamesButton class="btn-sm" />

			<button
				class="btn btn-sm hidden sm:block"
				disabled={!game.canUndo()}
				aria-label="Undo"
				onclick={() => undo()}
			>
				<UndoIcon />
			</button>

			<button
				class="btn btn-sm hidden sm:block"
				disabled={!game.canRedo()}
				aria-label="Redo"
				onclick={() => redo()}
			>
				<RedoIcon />
			</button>
		</div>

		<TentsGameRenderer class="py-4 cursor-pointer" {game} onpointermove={handlePointerMove} />

		<div class="grid grid-cols-2 justify-between sm:justify-center items-center gap-2 px-4">
			<div class="flex gap-2 sm:invisible">
				<button
					class="btn btn-sm"
					disabled={!game.canUndo()}
					aria-label="Undo"
					onclick={() => undo()}
				>
					<UndoIcon />
				</button>

				<button
					class="btn btn-sm"
					disabled={!game.canRedo()}
					aria-label="Redo"
					onclick={() => redo()}
				>
					<RedoIcon />
				</button>
			</div>

			<div class="flex gap-2 items-center justify-end">
				<button class="btn btn-sm sm:hidden" onclick={() => (isInfoModalOpen = true)}>
					<InfoSolidIcon class="size-5" />
				</button>

				{@render Settings('dropdown-top dropdown-end sm:hidden justify-self-end')}
			</div>
		</div>
	</div>
</GameLayout>

<TentsInfoModal bind:isOpen={isInfoModalOpen} />

{#snippet Settings(className: string)}
	<Dropdown class={className}>
		<DropdownButton class="btn-sm" aria-label="Show settings">
			<SettingsIcon class="w-5 h-5" />
		</DropdownButton>

		<DropdownContent class="w-80">
			<Collapse open={context.settingsManager.settings.autoFill}>
				<DropdownItem>
					<Toggle
						name="toggle grass autofill"
						bind:checked={context.settingsManager.settings.autoFill}>Grass Autofill</Toggle
					>
				</DropdownItem>

				<CollapseContent asSettings>
					<DropdownItem>
						<Toggle
							name="toggle cells not adjacent to trees"
							bind:checked={context.settingsManager.settings.autoFillNotAdjacentToTrees}
						>
							Cells not adjacent to trees
						</Toggle>
					</DropdownItem>

					<DropdownItem>
						<Toggle
							name="toggle cells around tents"
							bind:checked={context.settingsManager.settings.autoFillAroundTents}
						>
							Cells around tents
						</Toggle>
					</DropdownItem>

					<DropdownItem>
						<Toggle
							name="toggle rows/columns with 0 tents"
							bind:checked={context.settingsManager.settings.autoFillZeros}
						>
							Rows/columns with 0 Tents
						</Toggle>
					</DropdownItem>

					<DropdownItem>
						<Toggle
							name="toggle rows/columns with reached tents"
							bind:checked={context.settingsManager.settings.autoFillTentsAmountReached}
						>
							Rows/columns with reached tents
						</Toggle>
					</DropdownItem>
				</CollapseContent>
			</Collapse>
		</DropdownContent>
	</Dropdown>
{/snippet}
