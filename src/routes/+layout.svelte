<script lang="ts">
	import { onMount, type Snippet } from 'svelte';
	import '../app.css';
	import DmcaBodyOpacity from '$lib/components/DMCA/DmcaBodyOpacity/DMCABodyOpacity.svelte';
	import { theme } from '$lib/stores/theme.svelte';
	import { MetaTags } from 'svelte-meta-tags';
	import { page } from '$app/state';
	import { toast, Toaster } from 'svelte-sonner';
	import { musicPlayer, startedOnFirstUserInteraction } from '$lib/stores/musicPlayer.svelte';
	import { backgroundSoundManager } from '$lib/stores/backgroundSoundManager.svelte';
	import WarningSolidIcon from '$lib/components/Icons/WarningSolidIcon.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import CheckIcon from '$lib/components/Icons/CheckIcon.svelte';
	import { QueryClient, QueryClientProvider } from '@tanstack/svelte-query';
	import { Playlight } from '$lib/stores/playlightSdk.svelte';

	interface Props {
		children: Snippet;
	}

	let { children }: Props = $props();

	const queryClient = new QueryClient({
		defaultOptions: {
			queries: {
				refetchOnMount: false,
				refetchOnWindowFocus: false,
			},
		},
	});

	onMount(() => {
		window.addEventListener('vite:preloadError', () => {
			toast('New site update available!', {
				id: 'site-update-available',
				description: 'Reload the page to prevent errors.',
				action: {
					label: 'Reload',
					onClick: () => {
						window.location.reload();
					},
				},
				duration: 10_000,
			});
		});
	});

	function startMusicAndBackgroundSounds() {
		if (!startedOnFirstUserInteraction.value && musicPlayer.isReady) {
			musicPlayer.play();
			backgroundSoundManager.play();
			startedOnFirstUserInteraction.value = true;
		}
	}

	const pagesWithCustomMetaTags = [
		'/flappy-birdie',
		'/signin',
		'/signup',
		'/signout',
		'/reset-password',
		'/forgot-password',
		'/account',
		'/admin',
		'/wp-admin',
	];

	let canRenderMetaTags = $derived(
		!pagesWithCustomMetaTags.some((p) => page.url.pathname.includes(p)),
	);

	$effect(() => {
		Playlight.downloadAndInit();
	});
</script>

<svelte:window onclick={startMusicAndBackgroundSounds} onkeydown={startMusicAndBackgroundSounds} />

<QueryClientProvider client={queryClient}>
	{#if theme.themeColor && canRenderMetaTags}
		<MetaTags
			additionalMetaTags={[
				{
					name: 'theme-color',
					content: theme.themeColor,
				},
			]}
		/>
	{/if}

	{@render children()}

	<DmcaBodyOpacity />

	<Toaster
		position="bottom-right"
		gap={16}
		theme={theme.value as any}
		toastOptions={{
			unstyled: true,
			classes: {
				toast:
					'alert shadow-lg overflow-hidden flex flex-row items-center justify-stretch text-start w-full',
				success: 'alert-success',
				error: 'alert-error',
				warning: 'alert-warning',
				info: 'alert-info',
				actionButton: 'btn btn-sm btn-primary',
				cancelButton: 'btn btn-sm',
				description: 'text-sm',
				title: 'text-base font-medium',
				default: 'bg-base-100 dark:bg-base-300',
			},
		}}
	>
		<span class="loading loading-spinner size-6" slot="loading-icon"></span>
		<WarningSolidIcon class="size-6" slot="error-icon" />
		<InfoSolidIcon class="size-6" slot="info-icon" />
		<CheckIcon class="size-6" slot="success-icon" />
		<WarningSolidIcon class="size-6" slot="warning-icon" />
	</Toaster>
</QueryClientProvider>
