<script lang="ts">
	import { authClient } from '$lib/auth/client';
	import { createQuery } from '@tanstack/svelte-query';
	import AccountTemplate from '../../account/components/AccountTemplate.svelte';
	import { formatDateToYearMonthDay } from '$lib/functions/date/formatDateToYearMonthDay';
	import Calendar from '$lib/components/Calendar.svelte';
	import { maxLeaderboardDays } from '$lib/util/Leaderboard.svelte';
	import { isToday } from '$lib/functions/date/isToday';
	import { isYesterday } from '$lib/functions/date/isYesterday';
	import { startOfMonth } from '$lib/functions/date/startOfMonth';
	import { untrack } from 'svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import ChevronLeftIcon from '$lib/components/Icons/ChevronLeftIcon.svelte';
	import ChevronRightIcon from '$lib/components/Icons/ChevronRightIcon.svelte';
	import { subtract } from '$lib/functions/date/subtract';
	import { add } from '$lib/functions/date/add';
	import { isSameDay } from '$lib/functions/date/isSameDay';
	import RefreshIcon from '$lib/components/Icons/RefreshIcon.svelte';

	let session = authClient.useSession();
	let user = $derived($session.data?.user);
	let selectedDate = $state(new Date());
	let formattedDay = $derived(formatDateToYearMonthDay(selectedDate).split('/').join('-'));

	interface LeaderboardsResponse {
		key: string;
		total: number;
	}

	const leaderboards = $derived(
		createQuery({
			queryKey: ['admin/leaderboards', formattedDay],
			queryFn: async () => {
				const url = new URL(`${import.meta.env.VITE_API_URL}/leaderboards/day/${formattedDay}`);

				const res = await fetch(url, {
					credentials: 'include',
				});

				if (res.status >= 300) {
					throw new Error('Error loading leaderboards');
				}

				return res.json() as Promise<LeaderboardsResponse[]>;
			},
			enabled: user && user.role === 'admin',
		}),
	);

	const today = new Date();
	const thirtyDaysAgo = new Date(
		today.getFullYear(),
		today.getMonth(),
		today.getDate() - maxLeaderboardDays,
	);
	let isShowingCalendar = $state(false);
	let currentMonth = $state(untrack(() => startOfMonth(selectedDate)));

	$effect(() => {
		selectedDate;

		untrack(() => {
			isShowingCalendar = false;
		});
	});
</script>

<AccountTemplate title="Leaderboards">
	<div class="flex gap-4 items-center justify-center">
		<button
			class="btn btn-circle"
			aria-label="go to previous day"
			onclick={() => {
				selectedDate = subtract(selectedDate, 1, { min: thirtyDaysAgo });
			}}
			disabled={isSameDay(selectedDate, thirtyDaysAgo)}
		>
			<ChevronLeftIcon class="size-5" />
		</button>

		<Dropdown bind:open={isShowingCalendar} class="dropdown-center">
			<DropdownButton class="w-40">
				{#if isToday(selectedDate)}
					Today
				{:else if isYesterday(selectedDate)}
					Yesterday
				{:else}
					{Intl.DateTimeFormat('en', {
						day: 'numeric',
						month: 'long',
						year: 'numeric',
					}).format(selectedDate)}
				{/if}
			</DropdownButton>

			<DropdownContent class="w-auto xs:w-sm">
				<Calendar bind:selectedDate bind:currentMonth fromDate={thirtyDaysAgo} class="pt-4" />
			</DropdownContent>
		</Dropdown>

		<button
			class="btn btn-circle"
			aria-label="go to next day"
			onclick={() => {
				selectedDate = add(selectedDate, 1, { max: today });
			}}
			disabled={isSameDay(selectedDate, today)}
		>
			<ChevronRightIcon class="size-5" />
		</button>
	</div>

	{#if $leaderboards.isPending || $leaderboards.isFetching}
		<div
			class="loading loading-lg absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10"
		></div>
	{/if}

	{#if $leaderboards.data}
		<div class="overflow-x-auto">
			<table class="table table-fixed">
				<thead>
					<tr>
						<th class="min-w-max">Game</th>
						<th class="w-24">Players</th>
					</tr>
				</thead>
				<tbody>
					{#each $leaderboards.data.toSorted((a, b) => a.key.localeCompare(b.key)) as data}
						<tr>
							<td>
								{data.key.replace('daily-game-leaderboard-', '').replace(`-${formattedDay}`, '')}
							</td>
							<td>{data.total}</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>

		<div class="absolute bottom-0 right-0">
			<button
				class="btn btn-circle"
				onclick={() => {
					$leaderboards.refetch();
				}}
				aria-label="refresh leaderboard"
			>
				<RefreshIcon class="size-5" />
			</button>
		</div>
	{/if}
</AccountTemplate>
