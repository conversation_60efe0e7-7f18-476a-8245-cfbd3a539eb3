<script lang="ts">
	import Footer from '$lib/components/Footer.svelte';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { MetaTags } from 'svelte-meta-tags';
	import { crossfade, fade, fly } from 'svelte/transition';
	import { musicPlayer } from '$lib/stores/musicPlayer.svelte';
	import SlideShow from '$lib/components/SlideShow.svelte';
	import {
		elementTagsToIgnoreEvents,
		type ShortcutsTags,
	} from '$lib/util/Undoable/UndoableKeyboardListener';
	import AnimatedCard from '$lib/components/AnimatedCard.svelte';
	import { page } from '$app/state';
	import BuyMeACoffeeButton from '$lib/components/BuyMeACoffeeButton.svelte';
	import { theme } from '$lib/stores/theme.svelte';
	import { cn } from '$lib/util/cn';
	import { flip } from 'svelte/animate';
	import { quadInOut } from 'svelte/easing';
	import { afterNavigate } from '$app/navigation';
	import ChillContent from './Chill/ChillContent.svelte';
	import { wallpaper } from './wallpaper.svelte';
	import FeedbackIcon from '$lib/components/Icons/FeedbackIcon.svelte';
	import FeedbackModal from '$lib/components/FeedbackModal.svelte';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
	import { favoriteGames, games } from '$lib/stores/games.svelte';
	import StarSolidIcon from '$lib/components/Icons/StarSolidIcon.svelte';
	import StarIcon from '$lib/components/Icons/StarIcon.svelte';
	import CloseIcon from '$lib/components/Icons/CloseIcon.svelte';
	import { Playlight } from '$lib/stores/playlightSdk.svelte';
	import { siteSounds } from '$lib/stores/siteSounds.svelte';

	const fadeDuration = 3_000;
	const slideDuration = 300_000; // 5 min

	let activeTab = $state<'daily' | 'all' | 'chill'>('all');
	let isFeedbackModalOpen = $state(false);
	let isEditingFavorites = $state(false);

	const visibleGames = $derived.by(() => {
		let filteredGames = games;

		if (activeTab === 'daily') {
			filteredGames = games.filter((game) => game.daily);
		}

		return [...filteredGames].sort((a, b) => {
			if (
				(favoriteGames.isFavorite(a.key) && favoriteGames.isFavorite(b.key)) ||
				(!favoriteGames.isFavorite(a.key) && !favoriteGames.isFavorite(b.key))
			) {
				return games.indexOf(a) - games.indexOf(b);
			}

			if (favoriteGames.isFavorite(b.key)) {
				return 1;
			}

			return -1;
		});
	});

	function handleKeyDown(event: KeyboardEvent) {
		const tagName = (event.target as HTMLElement)?.tagName as unknown;

		if (tagName === 'BUTTON') {
			return;
		}

		if (elementTagsToIgnoreEvents.includes(tagName as ShortcutsTags)) {
			return;
		}

		const code = event.code.toLocaleLowerCase();

		if (code === 'space') {
			musicPlayer.togglePlay();
			event.preventDefault();
		}
	}

	const [send, receive] = crossfade({
		fallback: () => {
			return {
				duration: 300,
				easing: quadInOut,
				css: (t) => `
					opacity: ${t};
				`,
			};
		},
		duration: 300,
	});

	afterNavigate(() => {
		if (page.params.tab === 'chill') {
			activeTab = 'chill';
		} else if (page.params.tab === 'daily') {
			activeTab = 'daily';
		} else {
			activeTab = 'all';
		}
	});

	$effect(function loadFavorites() {
		favoriteGames.load();
	});

	$effect(function updatePlaylight() {
		Playlight.sdk?.setConfig({
			button: {
				visible: false,
			},
			exitIntent: {
				enabled: false,
			},
		});
	});
</script>

<svelte:body onkeypress={handleKeyDown} />

<MetaTags
	title="Play relaxing, casual games online"
	titleTemplate="Lofi and Games - %s"
	description="Play relaxing, casual games with a modern look and feel. Have a break from work, relax your mind, play to fall asleep, or play some games to kill time."
	canonical="https://www.lofiandgames.com"
	openGraph={{
		url: 'https://www.lofiandgames.com',
		images: [
			{
				url: 'https://www.lofiandgames.com/share.png',
				width: 1200,
				height: 630,
				alt: 'Lofi and Games',
			},
		],
		siteName: 'Lofi and Games',
		type: 'website',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Play Relaxing, Casual Games on Lofi and Games',
		image: 'https://www.lofiandgames.com/share.png',
		site: 'https://www.lofiandgames.com',
	}}
/>

<FeedbackModal bind:isOpen={isFeedbackModalOpen} context="Home" />

<div in:fade={{ delay: 500, duration: 300 }} out:fade={{ duration: 300 }}>
	{#key theme}
		<div transition:fade={{ duration: 1000 }}>
			<SlideShow
				class="fixed left-0 right-0 top-0 z-0 h-screen transition-transform duration-1000
				{activeTab === 'chill' ? '' : 'rotate-6 scale-125'}"
				style="background-position: {wallpaper.current?.backgroundPosition ?? 'center'};"
				images={wallpaper.images}
				onChange={(url) => wallpaper.changeByUrl(url)}
				{slideDuration}
				{fadeDuration}
			/>

			<div
				class="fixed -bottom-52 -left-52 -right-52 -top-52 z-10 bg-base-100 transition-all duration-1000"
				class:opacity-0={activeTab === 'chill'}
				class:opacity-90={activeTab !== 'chill'}
			></div>
		</div>
	{/key}
</div>

<PageTransition>
	<div class="relative z-20 min-h-screen">
		{#if activeTab === 'chill'}
			<ChillContent />
		{/if}

		{#if activeTab !== 'chill'}
			<div transition:fade={{ duration: 500 }}>
				<Navbar variant="transparent">
					<div role="tablist" class="tabs tabs-box">
						<a
							href="/"
							role="tab"
							class="tab tab-active"
							onclick={() => {
								siteSounds.homePlay.play();
							}}
						>
							Play
						</a>
						<a
							href="/chill"
							role="tab"
							class="tab"
							onclick={() => {
								siteSounds.homeChill.play();
							}}
						>
							Chill
						</a>
					</div>
				</Navbar>
			</div>

			<div class="bg-transparent" transition:fly={{ duration: 700, y: 200 }}>
				<div class="px-4 py-18">
					<div class="mx-auto relative flex flex-col items-center text-center">
						<h1 class="text-5xl sm:text-7xl font-bold">Lofi and Games</h1>
						<p class="pt-4">Play relaxing, casual games right from your browser</p>

						<div
							class="grid grid-cols-1 sm:grid-cols-2 gap-4 mx-auto pt-14 w-full max-w-60 sm:max-w-md"
						>
							<button class="btn btn-outline" onclick={() => (isFeedbackModalOpen = true)}>
								<FeedbackIcon class="size-6" /> Feedback
							</button>

							<BuyMeACoffeeButton variant="normal" class="w-full" />
						</div>
					</div>
				</div>

				{#if activeTab === 'daily' || activeTab === 'all' || activeTab === 'favorites'}
					<section
						transition:fade={{ duration: 300, easing: quadInOut }}
						class="relative z-10 px-4 py-6"
					>
						<div class="mx-auto max-w-7xl flex flex-col gap-4 items-center">
							<div class="flex items-center justify-between gap-4 w-full">
								<span class="btn btn-circle invisible"></span>

								<div role="tablist" class="tabs tabs-box inline-grid grid-cols-2">
									<a
										href="/"
										class="tab"
										role="tab"
										class:tab-active={activeTab === 'all'}
										data-sveltekit-noscroll
										onclick={() => {
											siteSounds.homeAllGames.play();
										}}
									>
										All
									</a>

									{#if islandSettings.settings.dailyGames}
										<a
											href="/daily"
											class="tab"
											role="tab"
											class:tab-active={activeTab === 'daily'}
											data-sveltekit-noscroll
											onclick={() => {
												siteSounds.homeDailyGames.play();
											}}
										>
											Daily
										</a>
									{/if}
								</div>

								<div
									class="tooltip before:hidden after:hidden lg:before:block lg:after:block tooltip-left 2xl:tooltip-top"
									data-tip={isEditingFavorites ? 'Finish' : 'Edit favorites'}
								>
									<a href="/" data-sveltekit-noscroll>
										<button
											class={cn('swap btn btn-circle', {
												'swap-active swap-rotate btn-warning': isEditingFavorites,
											})}
											aria-label="toggle edit favorites"
											onclick={(e) => {
												if (isEditingFavorites) {
													siteSounds.finishEditingFavorites.play();
												} else {
													siteSounds.editFavorites.play();
												}
												isEditingFavorites = !isEditingFavorites;
												e.preventDefault();
											}}
										>
											<div class="swap-on"><CloseIcon class="size-6" /></div>
											<div class="swap-off"><StarSolidIcon class="size-6" /></div>
										</button>
									</a>
								</div>
							</div>

							<!-- This div fixes a Safari bug -->
							<div>
								<div
									class="grid grid-cols-2 gap-4 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
								>
									{#each visibleGames as game, index (game.url)}
										{@const url = activeTab === 'daily' ? `${game.url}?daily` : game.url}
										{@const delay = 24 * index}
										<a
											class="relative"
											href={url}
											animate:flip={{ duration: 400, easing: quadInOut }}
											in:receive={{ key: game.name }}
											out:send={{ key: game.name }}
											onclick={(e) => {
												if (isEditingFavorites) {
													e.preventDefault();
												}
											}}
										>
											{#if isEditingFavorites}
												<div
													transition:fly={{
														y: -10,
														duration: 300,
														delay,
														easing: quadInOut,
													}}
													class="absolute right-2 top-2 z-10"
												>
													{#if favoriteGames.isFavorite(game.key)}
														<button
															class="btn btn-circle btn-warning"
															aria-label="Remove {game.name} from favorites"
															onclick={() => favoriteGames.remove(game.key)}
														>
															<StarSolidIcon class="size-6" />
														</button>
													{:else}
														<button
															class="btn btn-circle btn-warning btn-soft"
															aria-label="Add {game.name} to favorites"
															onclick={() => favoriteGames.add(game.key)}
														>
															<StarIcon class="size-6" />
														</button>
													{/if}
												</div>
											{/if}

											<AnimatedCard
												class={cn('size-full', {
													'cursor-default': isEditingFavorites,
												})}
												cardClass={cn(
													'size-full items-center bg-base-100 shadow-xl dark:bg-base-300',
												)}
												noZoomAnimation={isEditingFavorites}
												noHoverAnimation={isEditingFavorites}
											>
												{#if !isEditingFavorites}
													{#if favoriteGames.isFavorite(game.key)}
														<div
															transition:fly|global={{ y: -10, delay, duration: 300 }}
															class="btn btn-warning btn-soft btn-circle absolute right-2 top-2 z-10"
														>
															<StarSolidIcon class="size-6" />
														</div>
													{:else if game.tag}
														<span
															transition:fly|global={{ y: -10, delay, duration: 300 }}
															class="badge badge-primary absolute right-4 top-4 z-10"
														>
															{game.tag}
														</span>
													{/if}
												{/if}

												<figure
													class={cn('size-full aspect-square', {
														'grow *:size-full *:absolute *:top-1/2 *:left-1/2 *:-translate-x-1/2 *:-translate-y-1/2':
															game.coverType === 'full',
													})}
												>
													<game.cover />
												</figure>
												<div
													class={cn('py-4 px-2 text-center', {
														'absolute bottom-0 left-0 right-0': game.coverType === 'full',
													})}
												>
													<h2
														class="card-title text-xl select-none inline shadow-white dark:shadow-black"
														class:dark:text-white={game.coverType === 'full'}
													>
														{game.name}
													</h2>
												</div>
											</AnimatedCard>
										</a>
									{/each}
								</div>
							</div>

							<div class="w-full">
								<h2 class="text-3xl sm:text-4xl font-bold text-center mt-10 mb-8">More games</h2>

								<div class="playlight-widget-carousel w-full h-[330px]"></div>
							</div>
						</div>
					</section>
				{/if}

				<Footer class="bg-transparent pb-32 sm:pb-10" />
			</div>
		{/if}
	</div>
</PageTransition>
