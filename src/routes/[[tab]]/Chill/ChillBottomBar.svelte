<script lang="ts">
	import SoundIcon from '$lib/components/Icons/SoundIcon.svelte';
	import SoundWaveIcon from '$lib/components/Icons/SoundWaveIcon.svelte';
	import PlayerControls from '$lib/components/PlaylistPlayer/PlayerControls.svelte';
	import { musicPlayer } from '$lib/stores/musicPlayer.svelte';
	import { cn } from '$lib/util/cn';
	import { fade, fly } from 'svelte/transition';
	import { wallpaper } from '../wallpaper.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import { backgroundSoundManager } from '$lib/stores/backgroundSoundManager.svelte';
	import BackgroundSounds from '$lib/components/BackgroundSounds.svelte';
	import TimeIcon from '$lib/components/Icons/TimeIcon.svelte';
	import { getWidgetContext } from './Widgets/WidgetContext.svelte';
	import PlaylistButton from '$lib/components/PlaylistPlayer/MusicOptionsButton.svelte';
	import { siteSounds } from '$lib/stores/siteSounds.svelte';
	import RadioIcon from '$lib/components/Icons/RadioIcon.svelte';
	import { MediaQuery } from 'svelte/reactivity';
	import WallpaperAttributionLinks from './WallpaperAttributionLinks.svelte';

	let wallpaperAttribution = $derived(wallpaper.current?.attribution);
	let isShowingVolume = $state(false);
	let isShowingBackgroundSounds = $state(false);
	const widgetContext = getWidgetContext();
	const smallScreen = new MediaQuery('max-width: 639px');

	let isShowingTimer = $derived(widgetContext.isOpen('pomodoro-timer'));

	function resetOrSetBackgroundSoundVolumeToZero() {
		if (backgroundSoundManager.volume === 0) {
			backgroundSoundManager.volume = backgroundSoundManager.defaultVolume;
		} else {
			backgroundSoundManager.volume = 0;
		}
	}
</script>

<svelte:window
	onclick={() => {
		isShowingVolume = false;
		isShowingBackgroundSounds = false;
	}}
/>

{#snippet VolumeButton(className?: string)}
	<!-- svelte-ignore a11y_click_events_have_key_events -->
	<!-- svelte-ignore a11y_no_static_element_interactions -->
	<div
		class={cn('relative flex items-center justify-center', className)}
		onclick={(e) => e.stopPropagation()}
	>
		<button
			class="btn btn-ghost btn-circle"
			class:btn-active={isShowingVolume}
			onclick={() => {
				isShowingVolume = !isShowingVolume;
				isShowingBackgroundSounds = false;
			}}
			aria-label="Change volume"
		>
			<SoundIcon muted={musicPlayer.volume === 0} />
		</button>

		{#if isShowingVolume}
			<div
				transition:fly={{ y: 20 }}
				class="absolute -top-2 sm:-top-5 -translate-y-full right-0 w-56 flex items-center justify-center glass p-4 rounded-full z-10"
			>
				<input
					aria-label="music volume"
					aria-live="assertive"
					type="range"
					bind:value={musicPlayer.volume}
					min="0"
					max="1"
					step="0.01"
					class="range range-xs w-full"
				/>
			</div>
		{/if}
	</div>
{/snippet}

{#snippet BackgroundSoundsButton(className?: string)}
	<!-- svelte-ignore a11y_click_events_have_key_events -->
	<!-- svelte-ignore a11y_no_static_element_interactions -->
	<div
		class={cn('relative flex items-center justify-center', className)}
		onclick={(e) => e.stopPropagation()}
	>
		<button
			class={cn('btn btn-circle btn-ghost', className)}
			class:btn-active={isShowingBackgroundSounds}
			aria-label="Open background sounds"
			onclick={() => {
				isShowingBackgroundSounds = !isShowingBackgroundSounds;
				isShowingVolume = false;
			}}
		>
			<SoundWaveIcon class="size-8" />
		</button>

		{#if isShowingBackgroundSounds}
			<div
				transition:fly={{ y: 20 }}
				class="absolute -top-2 sm:-top-5 -translate-y-full left-0 sm:right-0 sm:left-auto w-72 flex flex-col glass p-4 rounded-box z-10"
			>
				<Toggle name="toggle background sounds" bind:checked={backgroundSoundManager.unmuted}>
					Background Sounds
				</Toggle>

				<div
					class={cn('overflow-hidden transition-all duration-500', {
						'max-h-0 opacity-0 pointer-events-none': backgroundSoundManager.muted,
						'max-h-[750px] py-2': !backgroundSoundManager.muted,
					})}
				>
					<div class="flex-center gap-2 pb-2">
						<button
							class="btn-ghost btn-sm btn-circle btn"
							onclick={resetOrSetBackgroundSoundVolumeToZero}
						>
							<SoundIcon muted={backgroundSoundManager.volume === 0} />
						</button>
						<input
							type="range"
							bind:value={backgroundSoundManager.volume}
							min="0"
							max="1"
							step="0.01"
							class="range range-xs"
						/>
					</div>

					<BackgroundSounds volumeSliderVariant="opaque" />
				</div>
			</div>
		{/if}
	</div>
{/snippet}

{#snippet TimerButton()}
	<button
		class="btn btn-circle btn-ghost hidden sm:flex"
		class:btn-active={isShowingTimer}
		aria-label="Open timer"
		onclick={() => {
			widgetContext.toggleWidget('pomodoro-timer');
		}}
	>
		<TimeIcon class="size-7" />
	</button>
{/snippet}

{#snippet RadioButton(className?: string, iconClassName?: string)}
	<button
		class={cn(
			'btn btn-circle btn-ghost',
			{
				'btn-active': musicPlayer.player === 'youtube',
			},
			className,
		)}
		onclick={() => {
			musicPlayer.togglePlayer();
		}}
		aria-label="Toggle Radio"
	>
		<RadioIcon class={cn('size-6', iconClassName)} />
	</button>
{/snippet}

<div
	class="fixed bottom-12 sm:bottom-8 left-1/2 -translate-x-1/2 flex items-center justify-center px-4 grow z-10 overflow-visible w-full"
>
	<div
		class="flex flex-col items-center justify-center gap-2"
		in:fade={{ duration: 300 }}
		out:fade={{ duration: 300 }}
	>
		{#if wallpaperAttribution}
			<WallpaperAttributionLinks attribution={wallpaperAttribution} />
		{/if}

		<div
			class="inline-flex gap-3 items-center justify-center px-4 py-3 flex-col rounded-box sm:flex-row sm:rounded-full relative max-w-80 sm:max-w-max"
		>
			<div class="glass rounded-[inherit] absolute inset-0 -z-10"></div>

			<PlayerControls class="items-end p-0" unmuteOnInteraction>
				{#snippet before()}
					{@render BackgroundSoundsButton('sm:hidden')}
				{/snippet}
				{#snippet after()}
					{@render VolumeButton('sm:hidden')}
				{/snippet}
			</PlayerControls>

			<!-- Music Info -->
			<div
				class={cn(
					'rounded-lg bg-base-300/30 px-2 py-1 flex items-center gap-3 h-full sm:w-52 md:w-60 lg:w-80 grow order-first w-full sm:order-none',
					{
						'pointer-events-none': isShowingVolume,
					},
				)}
			>
				<!-- Image -->
				<a
					target="_blank"
					rel="noreferrer noopener"
					href={musicPlayer.currentSong?.attribution.album.url}
					class="shrink-0 size-14 sm:size-10 rounded-sm overflow-hidden"
					aria-label={musicPlayer.currentSong?.attribution.album.name ??
						musicPlayer.currentSong?.attribution.work.name ??
						''}
				>
					{#if siteSounds.changeRadioStation.playing()}
						<video
							autoplay
							loop
							muted
							playsinline
							class="size-full rounded-sm"
							src="https://static.lofiandgames.com/videos/static.mp4"
						></video>
					{:else if musicPlayer?.currentSong?.attribution.album.picture}
						<div
							style={`background-image: url("${musicPlayer?.currentSong?.attribution.album.picture}")`}
							class="size-full bg-base-200 bg-cover bg-center dark:bg-base-100"
						></div>
					{/if}
				</a>

				<!-- Info -->
				<div class="flex flex-col w-full">
					<a
						class="text-base font-semibold line-clamp-1"
						target="_blank"
						rel="noreferrer noopener"
						href={musicPlayer?.currentSong?.attribution?.work.url}
					>
						{siteSounds.changeRadioStation.playing()
							? 'Tuning to lofi radio station...'
							: (musicPlayer?.currentSong?.attribution?.work.name ?? 'No music')}
					</a>

					<div class="text-sm font-normal opacity-75 flex flex-col sm:flex-row gap-1">
						<a
							class="line-clamp-1"
							target="_blank"
							rel="noreferrer noopener"
							href={musicPlayer?.currentSong?.attribution?.creator?.url}
						>
							{siteSounds.changeRadioStation.playing()
								? '...'
								: (musicPlayer?.currentSong?.attribution?.creator?.name ?? 'No artist')}
						</a>

						{#if musicPlayer?.currentSong?.attribution?.license}
							<span class="hidden sm:inline">•</span>

							<a
								class="line-clamp-1"
								target="_blank"
								rel="noreferrer noopener"
								href={musicPlayer?.currentSong?.attribution?.license.url}
							>
								{musicPlayer?.currentSong?.attribution?.license.name ?? ''}
							</a>
						{/if}
					</div>
				</div>

				<div class="flex flex-col gap-2">
					{#if musicPlayer.player === 'playlist'}
						<PlaylistButton
							class="dropdown-top dropdown-end"
							dropdownContentClass="glass"
							noBackdrop
							variant={smallScreen.current ? 'horizontal' : 'vertical'}
						/>
					{/if}

					{@render RadioButton('sm:hidden btn-sm', 'size-6')}
				</div>
			</div>

			{@render RadioButton('hidden sm:flex')}

			{@render VolumeButton('hidden sm:flex')}

			{@render BackgroundSoundsButton('hidden sm:flex')}

			{@render TimerButton()}
		</div>
	</div>
</div>
