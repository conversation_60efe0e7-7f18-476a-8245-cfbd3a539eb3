<script>
	import Collapse from '$lib/components/Collapse/Collapse.svelte';
	import CollapseContent from '$lib/components/Collapse/CollapseContent.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
</script>

<div class="p-8 pt-12">
	<div class="text-center text-2xl font-semibold mb-4">Settings</div>

	<Collapse open={islandSettings.settings.leaderboards}>
		<Toggle bind:checked={islandSettings.settings.leaderboards} name="Leaderboards">
			Leaderboards
		</Toggle>

		<CollapseContent asSettings>
			<Toggle
				bind:checked={islandSettings.settings.showLeaderboardsOnGameOver}
				name="show on game over"
			>
				Show on game over
			</Toggle>
		</CollapseContent>
	</Collapse>

	<Collapse open={islandSettings.settings.stats}>
		<Toggle bind:checked={islandSettings.settings.stats} name="Stats">Game Stats</Toggle>

		<CollapseContent asSettings>
			<Toggle bind:checked={islandSettings.settings.animatedStats} name="number animations">
				Number Animations
			</Toggle>
		</CollapseContent>
	</Collapse>

	<Toggle bind:checked={islandSettings.settings.dailyGames} name="Stats">Daily Games</Toggle>

	<Collapse open class="py-2 flex flex-col gap-2">
		<span class="text-sm">When opening, what should show up?</span>

		<CollapseContent asSettings>
			<label class="cursor-pointer gap-4 w-full flex justify-between items-center py-2">
				<span class="text-sm">Stats</span>
				<input
					type="radio"
					name="promote-strategy"
					class="radio"
					checked={islandSettings.settings.initialTab === 'stats' ||
						!islandSettings.settings.leaderboards}
					onchange={() => {
						islandSettings.settings.initialTab = 'stats';
					}}
				/>
			</label>

			<label class="cursor-pointer gap-4 w-full flex justify-between items-center py-2">
				<span class="text-sm">Leaderboards</span>
				<input
					type="radio"
					name="promote-strategy"
					class="radio"
					checked={islandSettings.settings.initialTab === 'leaderboard' &&
						islandSettings.settings.leaderboards}
					onchange={() => {
						islandSettings.settings.initialTab = 'leaderboard';
					}}
					disabled={!islandSettings.settings.leaderboards}
				/>
			</label>
		</CollapseContent>
	</Collapse>

	<Toggle bind:checked={islandSettings.settings.showExitIntent} name="Stats">
		Show "Leaving already?" message
	</Toggle>
</div>
