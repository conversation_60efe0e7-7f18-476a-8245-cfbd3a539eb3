<script lang="ts" generics="LiveKeys extends string, CustomKeys extends string">
	import PinIcon from '$lib/components/Icons/PinIcon.svelte';
	import { wait } from '$lib/functions/wait';
	import { cn } from '$lib/util/cn';
	import type { FixedStatsKey } from '$lib/util/Stats.svelte';
	import Stat from './Stat.svelte';
	import { untrack } from 'svelte';
	import { Leaderboard as LeaderboardModel } from '$lib/util/Leaderboard.svelte';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Alert from '$lib/components/Alert.svelte';
	import LeaderboardWithDatePicker from './leaderboard/LeaderboardWithDatePicker.svelte';
	import type { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import ChevronDownIcon from '$lib/components/Icons/ChevronDownIcon.svelte';

	interface Props {
		context: GameContext<any, any, any, any, LiveKeys, CustomKeys, any>;
		onChangeDailyGame: () => void;
		onReset: () => void;
		onSettings: () => void;
	}

	let { context: propsContext, onChangeDailyGame, onReset, onSettings }: Props = $props();

	// let overrideContext = $state<GameContext<any, any, any, any, LiveKeys, CustomKeys, any>>();
	let context = $derived(propsContext);
	// let contextStats = $derived(context.stats)!;
	let stats = $state(untrack(() => context.stats))!;
	let contextLeaderboard = $derived(context.leaderboard);
	let gameName = $derived(context.formatted.name);
	let gameVariant = $derived(context.formatted.variant);
	let leaderboardVariant = $derived(context.formatted.leaderboardVariant);
	let visibleStats = $derived(context.visibleStats);
	let isDailyGame = $derived(context.isPlayingDailyGame);

	const liveStats = $derived(
		Object.entries(stats.liveStats).map((entry) => {
			return {
				key: entry[0] as LiveKeys,
				value: entry[1],
			};
		}),
	);

	let leaderboard = $state<LeaderboardModel | undefined>(
		untrack(() => contextLeaderboard?.clone({ range: 'top-20' })),
	);

	let tab = $state<'stats' | 'leaderboard'>(
		untrack(() => {
			if (islandSettings.settings.initialTab === 'stats') {
				return 'stats';
			}

			if (!islandSettings.settings.leaderboards) {
				return 'stats';
			}

			if (!leaderboard) {
				return 'stats';
			}

			return 'leaderboard';
		}),
	);

	let canRunAnimations = $state(false);
	let firstAnimationDelay = $state(550);

	function selectVariant(
		variantValue: any,
		variant: any,
	) {
		console.log('select', variantValue, variant);
		// Clone stats from context with new game variant
		stats = context.stats!.clone({ gameVariant: variant.format(variantValue) });

		// Clone leaderboard from context with new game variant if available
		if (context.leaderboard) {
			leaderboard = context.leaderboard.clone({
				range: 'top-20',
				gameVariant: selectedVariant.formatted,
			});
		}

		console.log($state.snapshot(stats), $state.snapshot(leaderboard));
	}

	$effect.pre(() => {
		if (tab === 'stats') {
			canRunAnimations = false;
			firstAnimationDelay = 550;

			wait(300).then(() => {
				canRunAnimations = true;
			});
			wait(550).then(() => {
				firstAnimationDelay = 0;
			});
		}
	});

	const fixedAndCustomStats = $derived.by(() => {
		const values = visibleStats
			.map((stat) => {
				return {
					key: stat,
					value: stats.fixedStats[stat as FixedStatsKey] ?? stats.customStats[stat as CustomKeys],
				};
			})
			.filter((stat) => {
				if (stat.value === null || stat.value === undefined) {
					return false;
				}

				return true;
			});

		return values;
	});

	$effect(function loadLeaderboard() {
		// Track leaderboard
		leaderboard;

		untrack(() => {
			if (leaderboard && leaderboard?.loadState === 'idle') {
				leaderboard.load();
			}
		});
	});
</script>

{#snippet PinnedStat(stat: (typeof stats.pinnedStats)[number])}
	<div class="mb-4">
		{#if stat}
			<div
				class={cn(
					'h-[82px] pinned-stat border border-transparent bg-base-100 w-full  rounded-2xl p-2 pb-0',
					{
						'no-animation-duration': !canRunAnimations,
					},
				)}
			>
				<Stat
					{stat}
					{firstAnimationDelay}
					animated={islandSettings.settings.animatedStats}
					withTooltip
				/>
			</div>
		{:else}
			<div
				style="--tw-border-opacity: 0.5"
				class={cn(
					'h-[82px] empty-stat border border-base-content border-dashed flex items-center justify-center rounded-2xl p-2 pb-0',
					{
						'no-animation-duration': !canRunAnimations,
					},
				)}
			>
				<div class="stat p-0 text-center flex flex-col items-center">
					<div class="stat-title text-gray-300 opacity-60">No pinned stat</div>
					<div class="stat-value py-[9px]">
						<PinIcon class="size-10" />
					</div>
				</div>
			</div>
		{/if}
	</div>
{/snippet}

<button
	class="btn btn-circle btn-ghost btn-sm absolute top-6 left-6"
	onclick={onSettings}
	aria-label="Open island settings"
>
	<SettingsIcon class="size-5" />
</button>

<div class="flex flex-col gap-4 w-full pb-8 pt-12">
	<div class="px-8">
		<div class="text-center text-3xl font-semibold mb-1">{gameName}</div>
		<div
			class={cn('text-center text-lg transition-opacity duration-300', {
				'opacity-30 line-through': tab === 'leaderboard' && !leaderboardVariant,
			})}
		>
			{#if tab === 'stats' || !leaderboardVariant}
				{gameVariant}
			{:else}
				{leaderboardVariant}
			{/if}

			<!-- Variant selection dropdowns -->
			{#if allVariants.length > 1}
				<div class="flex flex-col gap-2 mt-4">
					{#each Object.keys(context.variants || {}) as variantKey}
						{@const variant = context.variants?.[variantKey]}
						{@const variantOptions = variant?.allValues}
						{@const formatter = variant?.format}
						{@const currentValue = variant?.fromGame(context.game)}
						{@const currentFormatted =
							formatter && currentValue ? formatter(currentValue) : String(currentValue)}

						{#if variantOptions}
							<Dropdown>
								<DropdownButton class="btn-sm w-full">
									<span class="flex items-center justify-between w-full">
										<span class="capitalize">{variantKey}: {currentFormatted}</span>
										<ChevronDownIcon class="size-4 ml-2" />
									</span>
								</DropdownButton>

								<DropdownContent menu class="w-full">
									{#each variantOptions as option}
										{@const optionFormatted = formatter ? formatter(option) : String(option)}

										<DropdownItem>
											<button
												class="w-full text-left"
												class:menu-active={JSON.stringify(currentValue) === JSON.stringify(option)}
												onclick={() => {
													selectVariant(option);
												}}
											>
												{optionFormatted}
											</button>
										</DropdownItem>
									{/each}
								</DropdownContent>
							</Dropdown>
						{/if}
					{/each}
				</div>
			{/if}

			<!-- <div class="grid gap-2 grid-cols-2 py-4">
				{#each allVariants as variant}
					<button
						class="btn btn-ghost btn-lg"
						class:btn-active={variant.formatted === gameVariant}
						onclick={() => {
							overrideContext = propsContext.shallowClone({
								settings: {
									defaultSettings: {
										...propsContext.settingsManager.defaultSettings,
									}
								}
							});
						}}>{variant.formatted}</button
					>
				{/each}
			</div> -->
		</div>
		<div class="text-center text-xl flex items-center justify-center">
			{#if isDailyGame && islandSettings.settings.dailyGames}
				<button
					class={cn(
						'link text-primary flex gap-2 items-center justify-center transition-colors duration-300',
						{
							'text-gray-600 line-through': tab === 'leaderboard',
						},
					)}
					disabled={tab === 'leaderboard'}
					onclick={onChangeDailyGame}
				>
					Daily Game
				</button>
			{/if}
		</div>
	</div>

	{#if leaderboard && islandSettings.settings.leaderboards && islandSettings.settings.stats}
		<div class="px-8">
			<div class="tabs tabs-box bg-transparent grid grid-cols-2 gap-2">
				<button
					class={cn('tab', {
						'tab-active': tab === 'stats',
					})}
					onclick={() => (tab = 'stats')}
				>
					Stats
				</button>

				<button
					class={cn('tab indicator w-full', {
						'tab-active': tab === 'leaderboard',
					})}
					onclick={() => (tab = 'leaderboard')}
				>
					Leaderboard
					<div class="indicator-item badge badge-sm badge-primary">Beta</div>
				</button>
			</div>
		</div>
	{/if}

	{#if tab === 'stats' || !leaderboard}
		{#if islandSettings.settings.stats}
			<!-- Live Stats -->
			<div class="grid grid-cols-1 gap-2 px-8">
				<div class="grid grid-cols-2 gap-2">
					<!-- Pinned -->

					{@render PinnedStat(stats.pinnedStats[0])}
					{@render PinnedStat(stats.pinnedStats[1])}

					<!-- Live stats -->
					{#each liveStats as stat}
						{@const isPinned = stats.isPinned(stat.key)}
						<div class="relative">
							<Stat
								stat={stat.value}
								withPin={stats.canPinStats || isPinned}
								{isPinned}
								onPinChange={(isPinned) =>
									isPinned ? stats.pinStat(stat.key) : stats.unpinStat(stat.key)}
								withTooltip
								animated={islandSettings.settings.animatedStats}
								{firstAnimationDelay}
							/>
						</div>
					{/each}
				</div>

				<div class="divider my-1"></div>

				<div class="grid grid-cols-2 gap-2 p-0 mt-2">
					{#each fixedAndCustomStats as stat}
						{@const isPinned = stats.isPinned(stat.key)}
						<Stat
							stat={stat.value}
							withPin={stats.canPinStats || isPinned}
							{isPinned}
							onPinChange={(isPinned) =>
								isPinned ? stats.pinStat(stat.key) : stats.unpinStat(stat.key)}
							withTooltip
							animated={islandSettings.settings.animatedStats}
							{firstAnimationDelay}
						/>
					{/each}
				</div>

				<button
					class="btn btn-full btn-soft rounded-full mt-2"
					onclick={(event) => {
						event.stopPropagation();
						onReset();
					}}
				>
					Reset
				</button>
			</div>
		{:else}
			<div class="flex flex-col items-center justify-center gap-4 px-8">
				<Alert title="Stats are disabled" class="w-full" />

				<button
					class="btn btn-primary rounded-full w-full"
					onclick={() => (islandSettings.settings.stats = true)}
				>
					Enable stats
				</button>
			</div>
		{/if}
	{:else if leaderboard}
		<LeaderboardWithDatePicker {leaderboard} />
	{/if}
</div>

<style>
	@keyframes pinAnimation {
		0% {
			transform: scale(1.5);
			opacity: 0;
		}

		50% {
			transform: scale(0.8);
		}

		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	.pinned-stat {
		animation: pinAnimation 300ms ease-in-out;
	}

	@keyframes emptyStatAnimation {
		0% {
			transform: scale(0.8);
			opacity: 0;
		}

		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	.empty-stat {
		animation: emptyStatAnimation 300ms ease-in-out;
	}

	.no-animation-duration {
		animation-duration: 0s !important;
	}
</style>
