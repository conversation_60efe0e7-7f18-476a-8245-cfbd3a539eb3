<script lang="ts">
	import { cn } from '$lib/util/cn';
	import MinusIcon from '$lib/components/Icons/MinusIcon.svelte';
	import { animate, motionValue, type SequenceOptions } from 'motion';
	import { untrack, type Snippet } from 'svelte';
	import ChevronLeftIcon from '$lib/components/Icons/ChevronLeftIcon.svelte';

	const islandClasses = {
		base: 'relative w-[130px] origin-top h-full flex items-center justify-center min-h-10 cursor-auto outline-hidden transition-shadow duration-300 overflow-x-clip overflow-y-clip',
		colors: 'bg-black',
		shape: {
			pill: 'cursor-pointer',
			'pill-expanded': 'z-40',
			expanded: 'z-40',
		},
		glow: 'shadow-[0px_0px_8px_3px_rgba(0,_0,_0,_1)] shadow-primary',
	};

	const childrenContainerClasses = {
		shape: {
			pill: 'size-full',
			'pill-expanded': '',
			expanded: '',
		},
	};

	function getMaxIslandHeight() {
		const vh = Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0);

		return vh - 32;
	}

	interface Props {
		children?: Snippet;
		onclick?: (event: MouseEvent) => void;
		onclose?: () => void;
		onback?: () => void;
		onLonelyIslandClick?: () => void;
		speed?: 'default' | 'fast';
		desiredExpandedWidth?: number;
		glow?: boolean;
		class?: string;
		islandContentClass?: string;
		disabled?: boolean;
		variant: 'pill' | 'expanded' | 'pill-expanded';
		withLonelyIsland?: boolean;
		withCloseButton?: boolean;
		withBackButton?: boolean;
		lonelyIslandAriaLabel: string;
		LonelyIslandIcon: Snippet<
			[
				{
					x: number | string;
					y: number | string;
					width: number | string;
					height: number | string;
				},
			]
		>;
	}

	let {
		children,
		disabled,
		variant,
		withLonelyIsland,
		glow,
		onback,
		onclose,
		onclick,
		onLonelyIslandClick,
		withCloseButton,
		withBackButton,
		class: classFromProps,
		islandContentClass,
		lonelyIslandAriaLabel,
		LonelyIslandIcon,
		desiredExpandedWidth = 384,
		speed = 'default',
		...props
	}: Props = $props();

	let previousVariant = variant;
	let previousGlow = glow;
	let canGlow = $state(true);
	let isFirstAnimation = true;
	let root: HTMLDivElement;
	let lonelyIslandContainer: SVGElement;
	let lonelyIslandEffectHelper: SVGCircleElement;
	let lonelyIsland: SVGCircleElement;
	let lonelyIslandIcon = $state() as SVGGElement;
	let island: HTMLDivElement;
	let islandContent: HTMLDivElement;
	let childrenContainer: HTMLDivElement;
	let isAnimating = $state(false);
	let clipPathRoundedAmount = motionValue(48);
	clipPathRoundedAmount.on('change', (latest) => {
		island.style.clipPath = `inset(0px round ${latest}px)`;
	});
	let animateOptions = $derived.by(() => {
		if (speed === 'default') {
			return;
		}

		return { duration: 0.55 } as SequenceOptions;
	});

	function animateVariantChange() {
		if (variant === 'pill') {
			const bouncyProps = {
				type: 'spring',
				duration: isFirstAnimation ? 0 : 0.8,
				bounce: 0.4,
			} as const;
			const previousVariantWasPill = previousVariant === 'pill';
			const previousVariantHadLonelyIsland = lonelyIsland.getAttribute('cx') === '55';

			if (withLonelyIsland) {
				return animate(
					[
						[
							lonelyIslandEffectHelper,
							{ cx: previousVariantHadLonelyIsland ? 10 : 20 },
							{ duration: 0 },
						],
						[islandContent, { opacity: previousVariantWasPill ? 1 : 0 }, { duration: 0, at: '<' }],
						[
							lonelyIslandContainer,
							previousVariantHadLonelyIsland ? { x: -5 } : { x: -14, opacity: 0 },
							{ duration: 0, at: '<' },
						],
						[
							lonelyIsland,
							previousVariantHadLonelyIsland ? { cx: 55 } : {},
							{ duration: 0, at: '<' },
						],
						'island-is-reset',
						[
							island,
							{ height: 40, x: -14 - 15 },
							{
								ease: 'easeInOut',
								duration: isFirstAnimation ? 0 : 0.3,
								at: previousVariantWasPill ? '<' : 'island-is-reset',
							},
						],
						[
							clipPathRoundedAmount,
							48,
							{
								duration: isFirstAnimation ? 0 : 0.3,
								at: '<',
							},
						],
						[
							island,
							{ width: 130, x: -14 },
							{
								duration: isFirstAnimation ? 0 : 0.8,
								type: 'spring',
								bounce: 0.5,
								at: previousVariantWasPill ? '<' : '-0.2',
							},
						],
						'position-is-correct',
						[
							root,
							{ transform: 'translateX(calc(-50% + 45px))' },
							{
								duration: isFirstAnimation ? 0 : 0.8,
								type: 'spring',
								bounce: 0.5,
								at: previousVariantWasPill ? '<' : '0',
							},
						],
						[
							lonelyIslandContainer,
							{ opacity: 1 },
							{ duration: 0.01, at: previousVariantWasPill ? '<' : 'position-is-correct' },
						],
						[lonelyIsland, { cx: 55 }, { ...bouncyProps, at: '<' }],
						[lonelyIslandContainer, { x: -5 }, { ...bouncyProps, at: '<' }],
						[lonelyIslandEffectHelper, { cx: 10 }, { ...bouncyProps, at: '<' }],
						[islandContent, { opacity: 1 }, { duration: 0.2, at: '-0.2' }],
						[lonelyIslandIcon, { opacity: 1 }, { duration: 0.2, at: '<' }],
					],
					animateOptions,
				);
			}

			return animate(
				[
					[islandContent, { opacity: previousVariantWasPill ? 1 : 0 }, { duration: 0 }],
					[lonelyIslandIcon, { opacity: 0 }, { duration: 0.1, at: '<' }],
					[lonelyIslandContainer, { x: -14 }, { duration: 0.1, at: '<' }],
					[lonelyIsland, { cx: 20 }, { duration: 0.1, at: '<' }],
					[lonelyIslandEffectHelper, { cx: 20 }, { duration: 0.1, at: '<' }],
					'island-is-reset',
					[
						island,
						{ height: 40, x: -14 - 15 },
						{
							ease: 'easeInOut',
							duration: isFirstAnimation ? 0 : 0.3,
							at: previousVariantWasPill ? '<' : 'island-is-reset',
						},
					],
					[
						clipPathRoundedAmount,
						48,
						{
							duration: isFirstAnimation ? 0 : 0.3,
							at: '<',
						},
					],
					[
						island,
						{ width: 130, x: 0 },
						{
							duration: isFirstAnimation ? 0 : 0.8,
							type: 'spring',
							bounce: 0.5,
							at: previousVariantWasPill ? '<' : '-0.2',
						},
					],
					[
						root,
						{ transform: 'translateX(0)' },
						{
							duration: isFirstAnimation ? 0 : 0.8,
							type: 'spring',
							bounce: 0.5,
							at: '<',
						},
					],
					'position-is-correct',
					[islandContent, { opacity: 1 }, { duration: 0.2, at: '-0.2' }],
				],
				animateOptions,
			);
		}

		const vw = Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0);

		const maxWidth = vw - 32;
		const width = Math.min(desiredExpandedWidth, maxWidth);

		islandContent.style.width = `${width}px`;
		island.style.height = 'auto';
		islandContent.style.height = 'auto';

		// Measure height just after setting the width
		const maxHeight = getMaxIslandHeight();
		const height = Math.min(islandContent.scrollHeight, maxHeight);

		const bouncyProps = {
			type: 'spring',
			duration: isFirstAnimation ? 0 : 0.6,
			bounce: 0.25,
		} as const;

		return animate(
			[
				[islandContent, { opacity: 0 }, { duration: 0 }],
				[lonelyIslandIcon, { opacity: 0 }, { duration: 0, at: '<' }],
				[lonelyIslandContainer, { x: -14 }, { duration: isFirstAnimation ? 0 : 0.1, at: '<' }],
				[lonelyIsland, { cx: 20 }, { duration: isFirstAnimation ? 0 : 0.1, at: '<' }],
				[lonelyIslandEffectHelper, { cx: 20 }, { duration: isFirstAnimation ? 0 : 0.1, at: '<' }],
				[root, { transform: 'translateX(0px)' }, { duration: isFirstAnimation ? 0 : 0.1, at: '<' }],
				'position-is-correct',
				[
					lonelyIslandContainer,
					{ opacity: 0, x: -40 },
					{ duration: 0.01, at: 'position-is-correct' },
				],
				[
					clipPathRoundedAmount,
					variant === 'pill-expanded' ? 150 : 48,
					{
						duration: bouncyProps.duration / 2,
						at: '<',
						ease: 'easeInOut',
					},
				],
				[island, { x: 0 }, { at: '<', ...bouncyProps }],
				[root, { transform: 'translateX(0)' }, { at: '<', ...bouncyProps }],
				[island, { width, height }, { at: '<', ...bouncyProps }],
				'finished',
				[islandContent, { opacity: 1 }, { duration: 0.2, at: 'finished' }],
			],
			animateOptions,
		);
	}

	$effect(() => {
		if (previousGlow !== untrack(() => glow)) {
			canGlow = false;
		}
		try {
			isAnimating = true;

			animateVariantChange()
				.then(() => {
					canGlow = true;
					previousGlow = untrack(() => glow);
				})
				.catch(console.error)
				.finally(() => (isAnimating = false));
		} catch (_) {
			// Ignore
			isAnimating = false;
		}
		isFirstAnimation = false;
		previousVariant = variant;
	});

	$effect(function animateHeightChange() {
		if (variant === 'expanded' && childrenContainer && !isAnimating) {
			let previousScrollHeight = -1;

			function animateHeight() {
				if (isAnimating) {
					return;
				}

				if (Math.abs(previousScrollHeight - childrenContainer.scrollHeight) > 20) {
					const bouncyProps = {
						type: 'spring',
						duration: 0.6,
						bounce: 0.3,
					} as const;
					const nextHeight = Math.min(getMaxIslandHeight(), childrenContainer.scrollHeight);
					island.style.overflowY = 'clip';

					animate([[island, { height: nextHeight }, { at: '<', ...bouncyProps }]]).then(() => {
						island.style.overflowY = 'auto';
					});

					previousScrollHeight = nextHeight;
				}
			}

			animateHeight();

			const mutationObserver = new MutationObserver(animateHeight);

			mutationObserver.observe(childrenContainer, {
				childList: true,
				subtree: true,
			});

			const resizeObserver = new ResizeObserver(animateHeight);

			resizeObserver.observe(childrenContainer);

			return () => {
				mutationObserver.disconnect();
				resizeObserver.disconnect();
			};
		}
	});
</script>

<div
	bind:this={root}
	class={cn('absolute origin-center left-1/2 top-0 -translate-x-1/2 z-30', classFromProps)}
>
	<svg
		bind:this={lonelyIslandContainer}
		class="h-10 w-20 absolute -right-10 -z-10 bg-inherit"
		viewBox="0 0 80 40"
		data-theme="dark"
	>
		<defs>
			<filter id="split-effect" width="400%" x="-150%" height="400%" y="-150%">
				<feGaussianBlur in="SourceGraphic" result="blur-sm" stdDeviation={4}></feGaussianBlur>
				<feColorMatrix
					in="blur-sm"
					mode="matrix"
					values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 25 -10"
					result="matrix"
				></feColorMatrix>
			</filter>
		</defs>
		<g filter="url(#split-effect)">
			<!-- svelte-ignore a11y_click_events_have_key_events -->
			<circle
				bind:this={lonelyIsland}
				class="outline-hidden"
				role="button"
				tabindex="0"
				cx="20"
				cy="20"
				r="16"
				fill="black"
				aria-label={lonelyIslandAriaLabel}
				aria-disabled={disabled}
				onclick={(event) => {
					event.stopPropagation();
					event.preventDefault();

					if (disabled) {
						return;
					}

					onLonelyIslandClick?.();
				}}
			/>
			<circle bind:this={lonelyIslandEffectHelper} cx="20" cy="20" r="20" fill="black" />
		</g>

		<g bind:this={lonelyIslandIcon} opacity="0" class="pointer-events-none">
			{@render LonelyIslandIcon?.({
				x: 39,
				y: 4,
				width: 32,
				height: 32,
			})}
		</g>
	</svg>

	<div
		bind:this={island}
		data-theme="dark"
		class={cn(islandClasses.base, islandClasses.colors, islandClasses.shape[variant], {
			[islandClasses.glow]: glow && canGlow,
		})}
		aria-disabled={disabled}
		aria-label="game stats island"
		onclick={(event) => {
			if (disabled) {
				return;
			}
			onclick?.(event);
		}}
		role="button"
		tabindex="0"
		style={`clip-path: inset(0px round 48px);`}
		{...props}
	>
		<div
			bind:this={islandContent}
			class={cn('absolute top-0 bottom-0 w-full overflow-auto', islandContentClass)}
		>
			{#if withBackButton}
				<div class="absolute top-6 left-6 p-0! overflow-clip">
					<button
						class="btn btn-circle btn-ghost btn-sm"
						onclick={(event) => {
							event.stopPropagation();
							onback?.();
						}}
					>
						<ChevronLeftIcon class="size-5" />
					</button>
				</div>
			{/if}
			{#if withCloseButton}
				<div class="absolute top-6 right-6 p-0! overflow-clip">
					<button
						class="btn btn-circle btn-ghost btn-sm"
						onclick={(event) => {
							event.stopPropagation();

							if (disabled) {
								return;
							}

							onclose?.();
						}}
					>
						<MinusIcon />
					</button>
				</div>
			{/if}

			<div class={childrenContainerClasses.shape[variant]} bind:this={childrenContainer}>
				{@render children?.()}
			</div>
		</div>
	</div>
</div>
