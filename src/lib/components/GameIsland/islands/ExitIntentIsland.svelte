<script lang="ts">
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import DotsHorizontalIcon from '$lib/components/Icons/DotsHorizontalIcon.svelte';
	import EyeIcon from '$lib/components/Icons/EyeIcon.svelte';
	import JoystickIcon from '$lib/components/Icons/JoystickIcon.svelte';
	import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
	import { Playlight } from '$lib/stores/playlightSdk.svelte';

	interface Props {
		onClose: () => void;
	}

	let { onClose }: Props = $props();

	const messages = [
		// 'The cure for boredom',
		// 'Ready for what’s next?',
		'Why stop the fun now?',
		"Games don't play themselves",
		'A new adventure awaits',
		"There's always time for one more",
	];

	let randomMessage = getRandomItemAt(messages);

	function hideExitIntent(e: MouseEvent) {
		e.stopPropagation();
		islandSettings.settings.showExitIntent = false;
		onClose();
	}
</script>

<div class="flex flex-col w-full p-8">
	<div class="relative">
		<Dropdown class="absolute -top-4 -right-4 dropdown-left">
			<DropdownButton class="btn btn-sm btn-ghost btn-circle" aria-label="open leaderboard menu">
				<DotsHorizontalIcon class="size-5" />
			</DropdownButton>

			<DropdownContent menu class="w-52">
				<DropdownItem>
					<button onclick={hideExitIntent}>
						<EyeIcon variant="invisible" class="size-5" />
						Hide this message
					</button>
				</DropdownItem>
			</DropdownContent>
		</Dropdown>

		<h3 class="text-3xl text-center font-medium mb-1">Leaving already?</h3>
	</div>

	<p class="text-center">{randomMessage}</p>

	<button
		class="btn btn-primary rounded-full w-full mt-6"
		onclick={(event) => {
			event.stopPropagation();
			Playlight.sdk?.setDiscovery(true);
		}}
	>
		<JoystickIcon class="size-7" />

		Play partner games
	</button>
</div>
