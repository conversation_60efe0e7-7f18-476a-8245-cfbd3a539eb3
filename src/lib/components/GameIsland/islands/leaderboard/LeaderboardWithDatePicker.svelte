<script lang="ts">
	import type { Leaderboard as LeaderboardModel } from '$lib/util/Leaderboard.svelte';
	import { tick, untrack } from 'svelte';
	import LeaderboardTable from './LeaderboardTable.svelte';
	import { isToday } from '$lib/functions/date/isToday';
	import { isYesterday } from '$lib/functions/date/isYesterday';
	import { startOfMonth } from '$lib/functions/date/startOfMonth';
	import { authClient } from '$lib/auth/client';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DotsHorizontalIcon from '$lib/components/Icons/DotsHorizontalIcon.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import WarningSolidIcon from '$lib/components/Icons/WarningSolidIcon.svelte';
	import FeedbackModal from '$lib/components/FeedbackModal.svelte';
	import { isSameDay } from '$lib/functions/date/isSameDay';
	import { add } from '$lib/functions/date/add';
	import { subtract } from '$lib/functions/date/subtract';
	import { cn } from '$lib/util/cn';
	import emblaCarouselSvelte from 'embla-carousel-svelte';
	import type { Action } from 'svelte/action';
	import type { EmblaCarouselType } from 'embla-carousel';
	import { WheelGesturesPlugin } from 'embla-carousel-wheel-gestures';

	interface Props {
		leaderboard: LeaderboardModel;
	}

	let { leaderboard: leaderboardFromProps }: Props = $props();

	const today = new Date();
	let emblaApi = $state<EmblaCarouselType>();

	let tabs = ['daily', 'weekly', 'monthly', 'yearly', 'all-time'] as const;
	type Tab = (typeof tabs)[number];

	let tab = $state<Tab>('daily');

	let leaderboard = $derived.by(() => {
		if (tab === 'daily') {
			return leaderboardFromProps.clone({
				range: 'top-50',
				frequency: 'daily',
			});
		}

		if (tab === 'weekly') {
			return leaderboardFromProps.clone({
				range: 'top-50',
				frequency: 'weekly',
			});
		}

		if (tab === 'monthly') {
			return leaderboardFromProps.clone({
				range: 'top-100',
				frequency: 'monthly',
			});
		}

		if (tab === 'yearly') {
			return leaderboardFromProps.clone({
				range: 'top-100',
				frequency: 'yearly',
			});
		}

		return leaderboardFromProps.clone({
			range: 'top-100',
			frequency: 'all-time',
		});
	});

	// Daily
	const amountOfDays = 14;
	let selectedDate = $state(untrack(() => leaderboardFromProps.date));
	// Weekly
	const amountOfWeeks = 5;
	const weeklyItems = getWeeklyRanges();
	let selectedWeek = $state(weeklyItems[weeklyItems.length - 1]);
	// Monthly
	const amountOfMonths = 12;
	const months = getMonths();
	let selectedMonth = $state(untrack(() => startOfMonth(leaderboardFromProps.date)));
	// Yearly
	const minYear = 2025;
	const canShowYears = new Date().getFullYear() > minYear;
	const amountOfYears = Math.min(5, new Date().getFullYear() - minYear + 1);
	const years = getYears();
	let selectedYear = $state(untrack(() => startOfMonth(leaderboardFromProps.date)));
	// All Time
	let isReportModalOpen = $state(false);
	let session = authClient.useSession();

	/** @todo Modify leaderboard to load different date ranges*/
	$effect(function loadLeaderboardAccordingToTab() {
		// Track
		tab;
		selectedDate;
		selectedMonth;
		selectedWeek;
		selectedYear;

		untrack(() => {
			if (tab === 'daily') {
				leaderboard.load(selectedDate);
			} else if (tab === 'weekly') {
				leaderboard.load(selectedWeek.from);
			} else if (tab === 'monthly') {
				leaderboard.load(selectedMonth);
			} else if (tab === 'yearly') {
				leaderboard.load(selectedYear);
			} else if (tab === 'all-time') {
				leaderboard.load();
			}
		});
	});

	const scrollRightAction: Action<HTMLElement, undefined> = () => {
		$effect(() => {
			// Track tab change
			tab;

			if (emblaApi) {
				tick().then(() => {
					emblaApi?.scrollTo(29, true);
				});
			}
		});
	};

	function getWeeklyRanges() {
		const firstDayOfMonth = new Date(
			leaderboard.date.getFullYear(),
			leaderboard.date.getMonth(),
			1,
		);
		let firstSunday: Date | null = null;
		let date = firstDayOfMonth;

		while (!firstSunday) {
			if (date.getDay() === 0) {
				firstSunday = date;
			}

			date = add(date, 1);
		}

		let nearRanges = [];

		for (let i = -amountOfWeeks; i < 5; i += 1) {
			const from = add(firstSunday, i * 7);
			const to = add(from, 6);

			nearRanges.push({ from, to });
		}

		const leaderboardRangeIndex = nearRanges.findIndex((range) => {
			return range.from <= leaderboard.date && range.to >= leaderboard.date;
		});

		const ranges = nearRanges.slice(
			leaderboardRangeIndex - amountOfWeeks + 1,
			leaderboardRangeIndex + 1,
		);

		return ranges;
	}

	function getMonths() {
		return Array.from({ length: amountOfMonths }).map((_, i) => {
			const month = new Date(
				leaderboard.date.getFullYear(),
				leaderboard.date.getMonth() - amountOfMonths + 1 + i,
				1,
			);

			return month;
		});
	}

	function getYears() {
		return Array.from({ length: amountOfYears }).map((_, i) => {
			const year = new Date(leaderboard.date.getFullYear() - amountOfYears + 1 + i, 0, 1);

			return year;
		});
	}
</script>

{#if $session.data?.user}
	<div>
		<div class="px-4">
			<div
				class={cn('tabs tabs-box tabs-sm bg-transparent grid grid-cols-4 gap-2', {
					'grid-cols-5': canShowYears,
				})}
			>
				<button
					class={cn('tab', {
						'tab-active': tab === 'daily',
					})}
					onclick={() => (tab = 'daily')}
				>
					Day
				</button>
				<button
					class={cn('tab', {
						'tab-active': tab === 'weekly',
					})}
					onclick={() => (tab = 'weekly')}
				>
					Week
				</button>
				<button
					class={cn('tab', {
						'tab-active': tab === 'monthly',
					})}
					onclick={() => (tab = 'monthly')}
				>
					Month
				</button>
				{#if canShowYears}
					<button
						class={cn('tab', {
							'tab-active': tab === 'yearly',
						})}
						onclick={() => (tab = 'yearly')}
					>
						Year
					</button>
				{/if}
				<button
					class={cn('tab', {
						'tab-active': tab === 'all-time',
					})}
					onclick={() => (tab = 'all-time')}
				>
					All <span class="hidden sm:inline">&nbsp;Time </span>
				</button>
			</div>
		</div>

		<div
			class={cn('embla overflow-hidden relative w-full', {
				// Left
				'before:w-8 before:h-full before:bg-linear-to-r before:from-black before:to-black/0 before:absolute before:left-0 before:top-0 before:bottom-0 before:z-10 before:pointer-events-none': true,
				// Right
				'after:w-8 after:h-full after:bg-linear-to-l after:from-black after:to-black/0 after:absolute after:right-0 after:top-0 after:bottom-0 after:z-10 after:pointer-events-none': true,
			})}
			use:emblaCarouselSvelte={{
				options: {
					align: 'center',
					axis: 'x',
					dragFree: true,
				},
				plugins: [WheelGesturesPlugin()],
			}}
			onemblaInit={(event) => {
				emblaApi = event.detail;
			}}
			use:scrollRightAction
		>
			<div
				class="embla__container flex gap-2 pt-4 [&_>*]:first-of-type:ml-8 [&_>*]:last-of-type:mr-8"
			>
				{#if tab === 'daily'}
					{#each Array.from({ length: amountOfDays }) as _, i}
						{@const date = subtract(today, amountOfDays - 1 - i, {
							min: leaderboard.firstAvailableDate,
						})}
						<button
							class={cn(
								'btn flex flex-col items-center justify-center gap-0 btn-square h-auto w-16 grow embla__slide',
								{
									'btn-primary': isSameDay(date, selectedDate),
								},
							)}
							onclick={() => {
								selectedDate = date;
							}}
						>
							<div
								class="text-xs leading-snug font-normal border-b border-b-base-content/15 w-full py-0.5"
							>
								{Intl.DateTimeFormat('en', {
									month: 'short',
								}).format(date)}
							</div>

							<div class="text-xl px-2 pt-1">{date.getDate()}</div>
							<div class="text-[0.69rem] leading-snug font-normal px-2 pb-1">
								{#if isToday(date)}
									Today
								{:else if isYesterday(date)}
									Yesterday
								{:else}
									{Intl.DateTimeFormat('en', {
										weekday: 'short',
									}).format(date)}
								{/if}
							</div>
						</button>
					{/each}
				{:else if tab === 'weekly'}
					{#each weeklyItems as range}
						<button
							class={cn(
								'btn flex flex-col items-center justify-center gap-0 btn-square h-auto w-24 grow embla__slide',
								{
									'btn-primary': selectedWeek.from === range.from,
								},
							)}
							onclick={() => {
								selectedWeek = range;
							}}
						>
							<div
								class="text-xs leading-snug font-normal border-b border-b-base-content/15 w-full py-0.5 flex items-center justify-center gap-1"
							>
								{Intl.DateTimeFormat('en', {
									month: 'short',
								}).format(range.from)}

								{#if range.from.getMonth() !== range.to.getMonth()}
									<span class="text-xs">{'/'}</span>

									{Intl.DateTimeFormat('en', {
										month: 'short',
									}).format(range.to)}
								{/if}
							</div>

							<div class="text-lg px-2 pt-1 flex items-center justify-center gap-1">
								{range.from.getDate()} <span class="text-xs">{'→'}</span>
								{range.to.getDate()}
							</div>
						</button>
					{/each}
				{:else if tab === 'monthly'}
					{#each months as month}
						<button
							class={cn('btn w-24 embla__slide', {
								'btn-primary': selectedMonth.getMonth() === month.getMonth(),
							})}
							onclick={() => {
								selectedMonth = month;
							}}
						>
							{Intl.DateTimeFormat('en', {
								month: 'long',
							}).format(month)}
						</button>
					{/each}
				{:else if tab === 'yearly'}
					{#each years as year}
						<button
							class={cn('btn w-24 embla__slide', {
								'btn-primary': selectedYear.getFullYear() === year.getFullYear(),
							})}
							onclick={() => {
								selectedYear = year;
							}}
						>
							{Intl.DateTimeFormat('en', {
								year: 'numeric',
							}).format(year)}
						</button>
					{/each}
				{/if}
			</div>
		</div>
	</div>
{/if}

<div class="relative">
	{#if $session.data?.user && leaderboard.loadState === 'success' && leaderboard.board.records.length > 0}
		<Dropdown class="dropdown-left absolute top-0 right-8">
			<DropdownButton class="btn-ghost btn-sm btn-circle">
				<DotsHorizontalIcon class="size-5" />
			</DropdownButton>

			<DropdownContent menu class="w-52">
				<DropdownItem>
					<button
						class="flex flex-row gap-2 items-center justify-start py-3 indicator w-full"
						onclick={() => (isReportModalOpen = true)}
					>
						<WarningSolidIcon class="size-5" />
						Report leaderboard
					</button>
				</DropdownItem>
			</DropdownContent>
		</Dropdown>

		<FeedbackModal
			bind:isOpen={isReportModalOpen}
			context="Report Leaderboard"
			extra={leaderboard.url}
			title="Report Leaderboard"
			description="If you think there is something wrong with this leaderboard, let us know!"
			feedbackLabel="Why are you reporting this leaderboard?"
			feedbackPlaceholder="e.g. Someone hacked it! Please ban @sombra"
			feedbackErrorLabel="Please enter a reason for reporting this leaderboard"
		/>
	{/if}

	<LeaderboardTable {leaderboard} />
</div>
