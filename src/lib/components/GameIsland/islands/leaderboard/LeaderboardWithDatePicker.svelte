<script lang="ts">
	import Calendar from '$lib/components/Calendar.svelte';
	import type { Leaderboard as LeaderboardModel } from '$lib/util/Leaderboard.svelte';
	import { untrack } from 'svelte';
	import LeaderboardTable from './LeaderboardTable.svelte';
	import Collapse from '$lib/components/Collapse/Collapse.svelte';
	import CollapseContent from '$lib/components/Collapse/CollapseContent.svelte';
	import { isToday } from '$lib/functions/date/isToday';
	import { isYesterday } from '$lib/functions/date/isYesterday';
	import { startOfMonth } from '$lib/functions/date/startOfMonth';
	import { authClient } from '$lib/auth/client';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DotsHorizontalIcon from '$lib/components/Icons/DotsHorizontalIcon.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import WarningSolidIcon from '$lib/components/Icons/WarningSolidIcon.svelte';
	import FeedbackModal from '$lib/components/FeedbackModal.svelte';
	import Alert from '$lib/components/Alert.svelte';
	import ChevronRightIcon from '$lib/components/Icons/ChevronRightIcon.svelte';
	import { isSameDay } from '$lib/functions/date/isSameDay';
	import { add } from '$lib/functions/date/add';
	import { subtract } from '$lib/functions/date/subtract';
	import ChevronLeftIcon from '$lib/components/Icons/ChevronLeftIcon.svelte';

	interface Props {
		leaderboard: LeaderboardModel;
	}

	let { leaderboard }: Props = $props();

	let selectedDate = $state(untrack(() => leaderboard.date));
	let currentMonth = $state(untrack(() => startOfMonth(leaderboard.date)));
	let isShowingCalendar = $state(false);
	let isReportModalOpen = $state(false);
	let session = authClient.useSession();

	$effect(() => {
		selectedDate;

		untrack(function loadLeaderboardWithSelectedDate() {
			isShowingCalendar = false;
			leaderboard.load(selectedDate);
		});
	});

	$effect(function restoreMonth() {
		if (isShowingCalendar) {
			untrack(() => {
				currentMonth = startOfMonth(selectedDate);
			});
		}
	});

	const today = new Date();
</script>

{#if $session.data?.user}
	<div class="px-8">
		<Collapse open={isShowingCalendar}>
			<div class="flex gap-4 items-center">
				<button
					class="btn btn-circle"
					aria-label="go to previous day"
					onclick={() => {
						selectedDate = subtract(selectedDate, 1, { min: leaderboard.firstAvailableDate });
					}}
					disabled={isSameDay(selectedDate, leaderboard.firstAvailableDate)}
				>
					<ChevronLeftIcon class="size-5" />
				</button>

				<button
					class="btn grow"
					class:btn-active={isShowingCalendar}
					onclick={() => (isShowingCalendar = !isShowingCalendar)}
				>
					{#if isToday(selectedDate)}
						Today
					{:else if isYesterday(selectedDate)}
						Yesterday
					{:else}
						{Intl.DateTimeFormat('en', {
							day: 'numeric',
							month: 'long',
							year: 'numeric',
						}).format(selectedDate)}
					{/if}
				</button>

				<button
					class="btn btn-circle"
					aria-label="go to next day"
					onclick={() => {
						selectedDate = add(selectedDate, 1, { max: today });
					}}
					disabled={isSameDay(selectedDate, today)}
				>
					<ChevronRightIcon class="size-5" />
				</button>
			</div>

			<CollapseContent class="p-0!">
				<Calendar
					bind:selectedDate
					bind:currentMonth
					fromDate={leaderboard.firstAvailableDate}
					class="pt-4"
				/>

				<Alert description="Leaderboards are stored for 30 days" />
			</CollapseContent>
		</Collapse>
	</div>
{/if}

{#if !isShowingCalendar}
	<div class="relative">
		{#if $session.data?.user && leaderboard.loadState === 'success' && leaderboard.board.records.length > 0}
			<Dropdown class="dropdown-left absolute top-0 right-8">
				<DropdownButton class="btn-ghost btn-sm btn-circle">
					<DotsHorizontalIcon class="size-5" />
				</DropdownButton>

				<DropdownContent menu class="w-52">
					<DropdownItem>
						<button
							class="flex flex-row gap-2 items-center justify-start py-3 indicator w-full"
							onclick={() => (isReportModalOpen = true)}
						>
							<WarningSolidIcon class="size-5" />
							Report leaderboard
						</button>
					</DropdownItem>
				</DropdownContent>
			</Dropdown>

			<FeedbackModal
				bind:isOpen={isReportModalOpen}
				context="Report Leaderboard"
				extra={leaderboard.url}
				title="Report Leaderboard"
				description="If you think there is something wrong with this leaderboard, let us know!"
				feedbackLabel="Why are you reporting this leaderboard?"
				feedbackPlaceholder="e.g. Someone hacked it! Please ban @sombra"
				feedbackErrorLabel="Please enter a reason for reporting this leaderboard"
			/>
		{/if}

		<LeaderboardTable {leaderboard} />
	</div>
{/if}
