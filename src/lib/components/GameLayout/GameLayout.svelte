<script lang="ts">
	import PhoneRotateLandscape from '../Icons/PhoneRotateLandscape.svelte';
	import type { Snippet } from 'svelte';
	import { cn } from '$lib/util/cn';
	import Navbar, { type NavbarProps } from '../Navbar/Navbar.svelte';

	type Orientation = 'landscape' | 'portrait' | 'all';

	interface Props {
		class?: string;
		mobileOrientation?: Orientation;
		noPadding?: boolean;
		navbarStyle?: 'default' | 'on-top' | 'on-top-as-glass';
		soundButtonAccentWhenPlaying?: boolean;
		navbarProps?: Pick<NavbarProps, 'profileButtonProps'>;
		Island: Snippet;
		children: Snippet;
	}

	let {
		class: classFromProps = '',
		mobileOrientation = 'portrait',
		noPadding = false,
		navbarStyle = 'default',
		soundButtonAccentWhenPlaying,
		navbarProps,
		Island,
		children,
	}: Props = $props();

	let isOnTop = $derived(['on-top', 'on-top-as-glass'].includes(navbarStyle));
</script>

<Navbar
	variant={navbarStyle === 'on-top'
		? 'transparent'
		: navbarStyle === 'on-top-as-glass'
			? 'glass'
			: 'normal'}
	{soundButtonAccentWhenPlaying}
	class={cn({
		'absolute top-0 left-0 right-0 z-20 grow-0': isOnTop,
	})}
	{...navbarProps}
>
	{@render Island()}
</Navbar>

<section
	class={cn(
		'touch-none flex justify-between size-full select-none relative p-4',
		{
			'p-0': noPadding,
			'min-h-screen-no-navbar': navbarStyle === 'default',
			'min-h-screen': isOnTop,
		},
		classFromProps,
	)}
>
	<main class="grow flex items-center justify-center relative">
		<div
			class={cn('h-full grow flex items-center justify-center relative max-w-[100vw]', {
				'portrait:invisible portrait:pointer-events-none lg:portrait:visible lg:portrait:pointer-events-auto':
					mobileOrientation === 'landscape',
				'landscape:invisible landscape:pointer-events-none lg:landscape:visible lg:landscape:pointer-events-auto':
					mobileOrientation === 'portrait',
			})}
		>
			{@render children()}
		</div>

		<div
			class={cn(
				'flex justify-center items-center absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex-col gap-4 text-lg text-center',
				{
					'lg:hidden portrait:hidden': mobileOrientation === 'portrait',
					'lg:hidden landscape:hidden': mobileOrientation === 'landscape',
					hidden: mobileOrientation === 'all',
				},
			)}
		>
			<PhoneRotateLandscape width="64" height="64" />

			<span>Rotate your device </span>
		</div>
	</main>

	<!-- <aside class="w-full max-w-64 bg-orange-50">Ads</aside> -->
</section>
