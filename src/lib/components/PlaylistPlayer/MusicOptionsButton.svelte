<script lang="ts">
	import { browser } from '$app/environment';
	import { playlistPlayer } from '$lib/stores/musicPlayer.svelte';
	import { cn } from '$lib/util/cn';
	import Dialog from '../Dialog.svelte';
	import Dropdown from '../Dropdown/Dropdown.svelte';
	import DropdownButton from '../Dropdown/DropdownButton.svelte';
	import DropdownContent from '../Dropdown/DropdownContent.svelte';
	import DropdownItem from '../Dropdown/DropdownItem.svelte';
	import DotsHorizontalIcon from '../Icons/DotsHorizontalIcon.svelte';
	import DotsVerticalIcon from '../Icons/DotsVerticalIcon.svelte';
	import PlaylistIcon from '../Icons/PlaylistIcon.svelte';
	import TrashIcon from '../Icons/TrashIcon.svelte';

	let isDropdownOpen = $state(false);
	let isDialogOpen = $state(false);

	interface Props {
		class?: string;
		dropdownContentClass?: string;
		variant?: 'vertical' | 'horizontal';
		noBackdrop?: boolean;
	}

	let {
		class: className = '',
		dropdownContentClass = '',
		variant = 'vertical',
		noBackdrop = false,
	}: Props = $props();
</script>

<Dropdown bind:open={isDropdownOpen} class={className} {noBackdrop}>
	<DropdownButton aria-label="Song options" class="btn btn-sm btn-ghost btn-circle">
		{#if variant === 'vertical'}
			<DotsVerticalIcon class="size-5" />
		{:else}
			<DotsHorizontalIcon class="size-5" />
		{/if}
	</DropdownButton>

	<DropdownContent menu class={cn('w-60', dropdownContentClass)}>
		<DropdownItem>
			<button
				onclick={() => {
					if (playlistPlayer.currentSong) {
						playlistPlayer.excludeSong(playlistPlayer.currentSong);
					}
					isDropdownOpen = false;
				}}
			>
				<TrashIcon class="size-6" />
				Remove from playlist
			</button>
		</DropdownItem>
		<DropdownItem>
			<button
				onclick={() => {
					isDialogOpen = true;
					isDropdownOpen = false;
				}}
			>
				<PlaylistIcon class="size-6" />
				Manage playlist
			</button>
		</DropdownItem>
	</DropdownContent>
</Dropdown>

{#if browser}
	<Dialog bind:isOpen={isDialogOpen} modalBoxClass="sm:max-w-xl px-0">
		<div class="overflow-x-auto mt-4">
			<table class="table">
				<!-- head -->
				<thead>
					<tr>
						<th>#</th>
						<th>Title</th>
						<th>Actions</th>
					</tr>
				</thead>
				<tbody>
					{#each playlistPlayer.playlist as song, index (song.url)}
						{@const isExcluded = playlistPlayer.isSongExcluded(song)}
						{@const isSelected = playlistPlayer.currentSong?.url === song.url}
						{@const isPlaying = isSelected && playlistPlayer.isPlaying}

						<tr
							class={{
								'cursor-pointer rounded-md': true,
								'opacity-50': isExcluded,
								'bg-base-200': isSelected,
							}}
							onclick={() => {
								if (isExcluded) {
									return;
								}
								playlistPlayer.currentSongIndex = index;
							}}
						>
							<td class:text-accent={isPlaying}>
								{index + 1}
							</td>
							<td>
								<div class="flex items-center gap-3">
									<div class="avatar">
										<div class="rounded size-12">
											<!-- Prevent images from loading when dialog is closed -->
											{#if isDialogOpen}
												<img
													src={song.attribution.album.picture}
													alt={song.attribution.album.name}
													loading="lazy"
												/>
											{:else}
												<div class="size-full bg-base-200 dark:bg-base-300"></div>
											{/if}
										</div>
									</div>
									<div>
										<div class="font-semibold" class:text-accent={isPlaying}>
											{song.attribution.work.name}
										</div>
										{#if song.attribution.creator}
											<div class="text-sm font-normal">{song.attribution.creator.name}</div>
										{/if}
										{#if song.attribution.license}
											<div class="text-xs font-light">{song.attribution.license?.name}</div>
										{/if}
									</div>
								</div>
							</td>
							<td>
								<div class="flex size-full justify-center">
									<button
										class="btn btn-sm btn-circle btn-ghost"
										aria-label="Remove from playlist"
										onclick={(e) => {
											e.stopPropagation();
											playlistPlayer.toggleSongExclusion(song);
										}}
									>
										<TrashIcon class="size-5" />
									</button>
								</div>
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	</Dialog>
{/if}
