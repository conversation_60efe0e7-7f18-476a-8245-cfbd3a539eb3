<script lang="ts">
	import type { Attribution } from '../../models/Attribution';

	interface Props {
		attributions: Attribution[];
	}

	let { attributions }: Props = $props();
</script>

<ul>
	{#each attributions as attr}
		<li>
			<a href={attr.work.url} target="_blank" rel="noreferrer noopener">"{attr.work.name}"</a>
			{#if attr.creator}
				by
				<a href={attr.creator.url} target="_blank" rel="noreferrer noopener">{attr.creator.name}</a
				>,
			{/if}
			{#if attr.license}
				used under
				<a href={attr.license.url} target="_blank" rel="noreferrer noopener">{attr.license.name}</a>
			{/if}
			{#if attr.modification}
				/ {attr.modification}
			{/if}
		</li>
	{/each}
</ul>
