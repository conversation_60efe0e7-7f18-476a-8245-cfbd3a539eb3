<script lang="ts">
	import GameSoundAttributions from './Attributions/GameSoundAttributions.svelte';
	import type { GameSoundResource } from '$lib/data/gameSounds';
	import Dialog from './Dialog.svelte';
	import type { Snippet } from 'svelte';

	interface Props {
		sounds?: Record<string, GameSoundResource>;
		isOpen?: boolean;
		children: Snippet;
	}

	let { sounds, isOpen = $bindable(false), children }: Props = $props();
</script>

<Dialog bind:isOpen>
	<article class="py-8">
		{@render children()}

		{#if sounds && Object.values(sounds).length > 0}
			<h2>Sound Effects Credits</h2>

			<p>
				The sound effects used on the game come from multiple parties. The credits and respective
				licenses are listed below:
			</p>

			<GameSoundAttributions {sounds} />
		{/if}

		<h2>Disclaimer</h2>

		<p>
			This game is a property of Lofi and Games. All code and assets are protected and must not be
			redistributed or used without prior permission.
		</p>
	</article>
</Dialog>
