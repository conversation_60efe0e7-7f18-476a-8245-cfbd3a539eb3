import { page } from '$app/state';
import ToastCard from '$lib/components/ToastCard.svelte';
import { untrack, type Component } from 'svelte';
import { toast } from 'svelte-sonner';

interface Notification {
	title: string;
	description: string;
	key: string;
	date: string;
	read?: boolean;
	link?: {
		name: string;
		url: string;
	};
	cta?: Component;
	feedback?: boolean;
	feedbackContext?: string;
	asToastOnPages?: string[];
}

let allNotifications: Notification[] = [
	{
		key: 'jigsaw-puzzle-beta',
		date: '2025-08-17',
		title: 'Jigsaw Puzzle in Beta',
		description:
			'New Jigsaw Puzzle game now available in Beta! Choose from beautiful images and different difficulty levels, or create your own puzzle using an image link. Try it out and share your feedback!',
		link: {
			name: 'Play Jigsaw Puzzle',
			url: '/jigsaw-puzzle',
		},
	},
	{
		key: 'safari-cache',
		date: '2025-08-04',
		title: 'Safari Issues',
		description:
			'The site is not loading on Safari? Please, clear your cache. On Mac: Hold Shift + click reload button in address bar; On iOS: Open Lofi and Games, then: Settings → Safari → Clear History and Website Data → Last Hour → Clear History. These steps can change depending on you Operating System version, but clearing the cache will fix the loading issues.',
		link: {
			name: 'More Safari Help',
			url: 'https://support.apple.com/en-us/105082',
		},
		asToastOnPages: ['/', '/chill', '/daily'],
	},
];

let notificationsWithReadState: Notification[] = $state([]);

const storageKey = 'notifications';

function readNotification(notification: Notification) {
	if (notification.read) {
		return;
	}

	notificationsWithReadState = notificationsWithReadState.map((n) => {
		if (n.key === notification.key) {
			return {
				...notification,
				read: true,
			};
		}

		return n;
	});

	localStorage.setItem(
		storageKey,
		JSON.stringify(
			notificationsWithReadState.map((notification) => {
				return {
					key: notification.key,
					date: notification.date,
					read: notification.read,
				};
			}),
		),
	);
}

function readAllNotifications() {
	allNotifications.forEach(readNotification);
}

let hasUnreadNotifications = $derived(
	notificationsWithReadState.some((notification) => !notification.read),
);

function handleStorageChange() {
	checkNotificationsFromStorage();
}

function checkNotificationsFromStorage() {
	const storedNotifications = JSON.parse(localStorage.getItem(storageKey) ?? '[]');

	notificationsWithReadState = allNotifications.map((n) => {
		const item = storedNotifications.find(
			(item: any) => item.key === n.key && item.date === n.date,
		);

		if (item) {
			return {
				...n,
				read: item.read,
			};
		}

		return n;
	});
}

/**
 * Must ignore first check because svelte assigns the pathname to / first,
 * then it changes to the actual page
 **/
let isFirstPageCheck = $state(true);
const toastInitWaitTime = 500;

$effect.root(() => {
	$effect.pre(() => {
		untrack(() => {
			checkNotificationsFromStorage();
		});
	});

	$effect(() => {
		untrack(() => {
			window.addEventListener('storage', handleStorageChange);
		});
	});

	$effect(function showToastOnPageView() {
		const pathname = page.url.pathname;

		if (isFirstPageCheck) {
			isFirstPageCheck = false;
			return;
		}

		untrack(() => {
			setTimeout(() => {
				const notificationsToShow = notifications.all
					.filter((notifications) => !notifications.read)
					.filter((notification) => notification.asToastOnPages?.includes(pathname))
					.reverse();

				notificationsToShow.forEach((notification) => {
					toast.custom(ToastCard, {
						id: notification.key,
						componentProps: {
							title: notification.title,
							description: notification.description,
							action: {
								label: 'Got it',
								onClick() {
									notifications.readNotification(notification);
								},
							},
						},
						duration: Number.POSITIVE_INFINITY,
						onDismiss() {
							notifications.readNotification(notification);
						},
					});
				});
			}, toastInitWaitTime);
		});
	});
});

export const notifications = {
	get all() {
		return notificationsWithReadState;
	},
	get hasUnreadNotifications() {
		return hasUnreadNotifications;
	},
	readNotification,
	readAllNotifications,
};
