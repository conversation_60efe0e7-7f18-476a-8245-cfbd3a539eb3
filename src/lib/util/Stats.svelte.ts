import { db } from '$lib/db/db';
import { handleError } from '../../hooks.client';
import { numberOrZero } from './numberOrZero';
import { Timer } from './Timer.svelte';

interface DataPoint {
	name: string;
	description?: string;
	unit: 'plain' | 'percentage';
}

interface DataPointWithTime extends Omit<DataPoint, 'unit'> {
	unit: 'plain' | 'percentage' | 'time';
}

interface LiveDataPointSettings<LiveStatsKey extends string, MetricsKey extends string>
	extends DataPoint {
	value: () => number;
	/**
	 * Function to update the isNewBest property.
	 * This is called AFTER the fixed and custom data points are updated.
	 * If useAsBest is true on min or max metrics, it will be overriden
	 **/
	calcIsNewBest?: (context: Stats<LiveStatsKey, MetricsKey>) => boolean;
	metrics?: {
		total: {
			key: MetricsKey;
			name: string;
		};
		average: {
			key: MetricsKey;
			name: string;
		};
		min: {
			key: Met<PERSON>s<PERSON><PERSON>;
			name: string;
			useAsBest?: boolean;
		};
		max: {
			key: Met<PERSON><PERSON><PERSON>ey;
			name: string;
			useAsBest?: boolean;
		};
	};
}

interface CustomDataPointSettings<LiveStatsKey extends string, CustomStatsKey extends string>
	extends DataPointWithTime {
	updateOrder: number;
	/**
	 * Returns a new updated value.
	 * Attention: The onUpdate callback is called in order according to
	 * the updateOrder prop. So if a callback depends
	 * on another one being called first, put the data points in
	 * the correct sequence
	 **/
	onUpdate: (
		currentValue: number,
		stats: Stats<LiveStatsKey, CustomStatsKey>,
		gameWon?: boolean,
	) => number;
	/**
	 * Function to update the isNewBest property.
	 * This is called BEFORE the live data points are updated.
	 **/
	calcIsNewBest?: (previousValue: number, currentValue: number) => boolean;
}

// Inner stats interfaces

export interface LiveDataPoint<LiveStatsKey extends string, CustomStatsKey extends string>
	extends Omit<LiveDataPointSettings<LiveStatsKey, CustomStatsKey>, 'unit' | 'value'>,
		DataPointWithTime {
	value: number;
	isNewBest: boolean;
}

export interface CustomDataPoint<LiveStatsKey extends string, CustomStatsKey extends string>
	extends CustomDataPointSettings<LiveStatsKey, CustomStatsKey> {
	value: number;
	isNewBest: boolean;
}

export interface FixedDataPoint extends Omit<DataPoint, 'unit'> {
	value: number;
	unit: 'plain' | 'percentage' | 'time';
	isNewBest: boolean;
}

type FixedStats = {
	bestTime: FixedDataPoint;
	averageTime: FixedDataPoint;
	totalTime: FixedDataPoint;
	totalGames: FixedDataPoint;
	wonGames: FixedDataPoint;
};

export type FixedStatsKey = keyof FixedStats;

type BestTimeStrategy = 'smaller-is-best' | 'bigger-is-best';
type BestTimeSaveStrategy = 'on-win' | 'always';

export interface StatsProps<
	LiveStatsKey extends string,
	CustomStatsKey extends string,
	PinnedStats extends [
		LiveStatsKey | CustomStatsKey | FixedStatsKey | 'time' | null,
		LiveStatsKey | CustomStatsKey | FixedStatsKey | 'time' | null,
	] = [
		LiveStatsKey | CustomStatsKey | FixedStatsKey | 'time' | null,
		LiveStatsKey | CustomStatsKey | FixedStatsKey | 'time' | null,
	],
> {
	game: string;
	gameVariant: string;
	timer: Timer;
	liveStats: Record<LiveStatsKey, LiveDataPointSettings<LiveStatsKey, CustomStatsKey>>;
	initialPinnedStats: Array<PinnedStats[number]>;
	bestTimeStrategy?: BestTimeStrategy;
	bestTimeSaveStrategy?: BestTimeSaveStrategy;
	beforeReset?: () => void;
	afterReset?: () => void;
}

export class Stats<
	LiveStatsKey extends string = '',
	CustomStatsKey extends string = '',
	PinnedStats extends [
		LiveStatsKey | CustomStatsKey | FixedStatsKey | 'time' | null,
		LiveStatsKey | CustomStatsKey | FixedStatsKey | 'time' | null,
	] = [
		LiveStatsKey | CustomStatsKey | FixedStatsKey | 'time' | null,
		LiveStatsKey | CustomStatsKey | FixedStatsKey | 'time' | null,
	],
> {
	private _game: string;
	private _gameVariant: string;
	private _bestTimeStrategy: BestTimeStrategy;
	private _bestTimeSaveStrategy: BestTimeSaveStrategy;
	mustRefreshPageDueToError = $state(false);
	createdAt: Date;
	timer = $state<Timer>() as Timer;
	fixedStats: FixedStats = $state({
		bestTime: {
			name: 'Best Time',
			unit: 'time',
			value: 0,
			pinned: false,
			isNewBest: false,
		},
		totalTime: {
			name: 'Total Time',
			unit: 'time',
			value: 0,
			pinned: false,
			isNewBest: false,
		},
		averageTime: {
			name: 'Average Time',
			unit: 'time',
			value: 0,
			pinned: false,
			isNewBest: false,
		},
		totalGames: {
			name: 'Total Games',
			unit: 'plain',
			value: 0,
			pinned: false,
			isNewBest: false,
		},
		wonGames: {
			name: 'Won Games',
			unit: 'plain',
			value: 0,
			pinned: false,
			isNewBest: false,
		},
	});
	liveStats = $state<Record<LiveStatsKey | 'time', LiveDataPoint<LiveStatsKey, CustomStatsKey>>>(
		{} as any,
	)!;
	customStats = $state<Record<CustomStatsKey, CustomDataPoint<LiveStatsKey, CustomStatsKey>>>(
		{} as any,
	)!;
	private pinnedStatsKeys: Array<LiveStatsKey | CustomStatsKey | FixedStatsKey | 'time' | null> =
		$state([null, null]);
	pinnedStats: Array<
		| LiveDataPoint<LiveStatsKey, CustomStatsKey>
		| CustomDataPoint<LiveStatsKey, CustomStatsKey>
		| FixedDataPoint
		| null
	> = $derived.by(() => {
		return this.pinnedStatsKeys.map(
			(key) =>
				this.liveStats[key as LiveStatsKey] ??
				this.customStats[key as CustomStatsKey] ??
				this.fixedStats[key as FixedStatsKey] ??
				null,
		);
	});
	private beforeReset?: () => void;
	private afterReset?: () => void;

	constructor(props: StatsProps<LiveStatsKey, CustomStatsKey, PinnedStats>) {
		this.pinnedStatsKeys[0] = props.initialPinnedStats[0] || null;
		this.pinnedStatsKeys[1] = props.initialPinnedStats[1] || null;
		this.createdAt = new Date();
		this._game = props.game;
		this._gameVariant = props.gameVariant;
		this.timer = props.timer;
		this.beforeReset = props.beforeReset;
		this.afterReset = props.afterReset;
		let that = this;
		this._bestTimeStrategy = props.bestTimeStrategy ?? 'smaller-is-best';
		this._bestTimeSaveStrategy = props.bestTimeSaveStrategy ?? 'on-win';

		const timeStat: LiveDataPointSettings<LiveStatsKey, CustomStatsKey> = {
			name: 'Time',
			value: () => this.timer.elapsedTime,
			unit: 'time' as any,
			calcIsNewBest: () => {
				return this.fixedStats.bestTime.isNewBest;
			},
		};

		const liveStats = [['time', timeStat], ...Object.entries(props.liveStats)] as [
			LiveStatsKey,
			Record<LiveStatsKey, LiveDataPointSettings<LiveStatsKey, CustomStatsKey>>,
		][];

		liveStats.forEach((entry) => {
			const key = entry[0] as LiveStatsKey;
			let stat = entry[1] as LiveDataPointSettings<LiveStatsKey, CustomStatsKey>;
			let proxiedStat = {
				...stat,
				isNewBest: false,
				get value() {
					return stat.value();
				},
			} as LiveDataPoint<LiveStatsKey, CustomStatsKey>;

			this.liveStats[key] = proxiedStat;

			if (stat.metrics) {
				Object.entries(stat.metrics).forEach((entry) => {
					const type = entry[0] as keyof typeof stat.metrics;
					const value = entry[1];

					if (type === 'total') {
						this.customStats[value.key] = {
							name: value.name,
							unit: 'plain',
							isNewBest: false,
							value: 0,
							updateOrder: 0,
							onUpdate: (currentValue, stats) => {
								return numberOrZero(currentValue) + numberOrZero(stats.liveStats[key].value);
							},
						};
					} else if (type === 'average') {
						this.customStats[value.key] = {
							name: value.name,
							unit: 'plain',
							isNewBest: false,
							value: 0,
							updateOrder: 2,
							onUpdate: (currentValue, stats) => {
								const totalKey = stat.metrics!.total.key;
								const total = numberOrZero(this.customStats[totalKey].value);

								return total / (numberOrZero(stats.fixedStats.totalGames.value) || 1);
							},
						};
					} else {
						this.customStats[value.key] = {
							name: value.name,
							unit: 'plain',
							isNewBest: false,
							value: 0,
							updateOrder: 1,
							onUpdate: (currentValue, stats) => {
								const current = numberOrZero(stats.liveStats[key].value);
								currentValue = numberOrZero(currentValue);

								if (currentValue === 0) {
									return current;
								}

								return type === 'max'
									? Math.max(currentValue, current)
									: Math.min(currentValue, current);
							},
							calcIsNewBest: (previousValue, currentValue) => {
								return type === 'max' ? currentValue > previousValue : currentValue < previousValue;
							},
						};
					}
				});

				const newBestStatKey = stat.metrics.min.useAsBest
					? stat.metrics.min.key
					: stat.metrics.max.useAsBest
						? stat.metrics.max.key
						: null;

				if (newBestStatKey) {
					proxiedStat.calcIsNewBest = (context) => {
						return context.customStats[newBestStatKey].isNewBest;
					};
				}
			}
		});

		Object.entries(this.fixedStats).forEach((entry) => {
			const key = entry[0] as FixedStatsKey;
			let stat = entry[1] as FixedDataPoint;
			let proxiedStat = {
				...stat,
				get pinned() {
					return that.isPinned(key);
				},
				set pinned(newPinned) {
					if (newPinned) {
						that.pinStat(key);
					} else {
						that.unpinStat(key);
					}
				},
			} as FixedDataPoint;

			this.fixedStats[key] = proxiedStat;
		});
	}

	pinStat(key: (typeof this.pinnedStatsKeys)[number]) {
		if (!this.canPinStats) {
			return;
		}

		if (this.isPinned(key)) {
			return;
		}

		if (this.pinnedStatsKeys[0] === null) {
			this.pinnedStatsKeys[0] = key;
		} else if (this.pinnedStatsKeys[1] === null) {
			this.pinnedStatsKeys[1] = key;
		}

		this.savePinnedStats();
	}

	unpinStat(key: (typeof this.pinnedStatsKeys)[number]) {
		if (this.pinnedStatsKeys[0] === key) {
			this.pinnedStatsKeys[0] = null;
		} else if (this.pinnedStatsKeys[1] === key) {
			this.pinnedStatsKeys[1] = null;
		}

		this.savePinnedStats();
	}

	isPinned(key: (typeof this.pinnedStatsKeys)[number]) {
		return this.pinnedStatsKeys[0] === key || this.pinnedStatsKeys[1] === key;
	}

	get game() {
		return this._game;
	}

	get gameVariant() {
		return this._gameVariant;
	}

	get canPinStats() {
		return this.pinnedStatsKeys[0] === null || this.pinnedStatsKeys[1] === null;
	}

	updateStats(gameWon?: boolean) {
		// Reset isNewBest from fixed stats
		this.fixedStats.bestTime.isNewBest = false;

		// Fixed stats
		this.fixedStats.totalGames.value = numberOrZero(this.fixedStats.totalGames.value) + 1;
		this.fixedStats.totalTime.value =
			numberOrZero(this.fixedStats.totalTime.value) + this.timer.elapsedTime;
		this.fixedStats.averageTime.value =
			numberOrZero(this.fixedStats.totalTime.value) /
			(numberOrZero(this.fixedStats.totalGames.value) || 1);

		if (gameWon) {
			this.fixedStats.wonGames.value += 1;
		}

		if (
			this._bestTimeSaveStrategy === 'always' ||
			(gameWon && this._bestTimeSaveStrategy === 'on-win')
		) {
			const isNewBest =
				this._bestTimeStrategy === 'smaller-is-best'
					? this.timer.elapsedTime < this.fixedStats.bestTime.value
					: this.timer.elapsedTime > this.fixedStats.bestTime.value;

			if (isNewBest) {
				this.fixedStats.bestTime.isNewBest = true;
			}

			if (isNewBest || this.fixedStats.bestTime.value === 0) {
				this.fixedStats.bestTime.value = this.timer.elapsedTime;
			}
		}

		// Custom stats
		Object.values<CustomDataPoint<LiveStatsKey, CustomStatsKey>>(this.customStats)
			.sort((a, b) => a.updateOrder - b.updateOrder)
			.forEach((stat) => {
				const previousValue = numberOrZero(stat.value);
				const newValue = numberOrZero(stat.onUpdate(stat.value, this, gameWon));

				stat.value = newValue;
				stat.isNewBest = stat.calcIsNewBest?.(previousValue, newValue) ?? false;
			});

		// Live stats
		Object.values(this.liveStats).forEach((stat) => {
			stat.isNewBest = stat.calcIsNewBest?.(this) ?? false;
		});

		this.save();
	}

	resetIsNewBest() {
		this.fixedStats.averageTime.isNewBest = false;
		this.fixedStats.bestTime.isNewBest = false;
		this.fixedStats.totalGames.isNewBest = false;
		this.fixedStats.totalTime.isNewBest = false;
		this.fixedStats.wonGames.isNewBest = false;

		Object.values(this.customStats).forEach((stat) => {
			(stat as CustomDataPoint<LiveStatsKey, CustomStatsKey>).isNewBest = false;
		});
		Object.values(this.liveStats).forEach((stat) => {
			(stat as LiveDataPoint<LiveStatsKey, CustomStatsKey>).isNewBest = false;
		});
	}

	reset() {
		this.beforeReset?.();
		this.resetIsNewBest();

		this.fixedStats.averageTime.value = 0;
		this.fixedStats.bestTime.value = 0;
		this.fixedStats.totalGames.value = 0;
		this.fixedStats.totalTime.value = 0;
		this.fixedStats.wonGames.value = 0;

		Object.values(this.customStats).forEach((stat) => {
			(stat as CustomDataPoint<LiveStatsKey, CustomStatsKey>).value = 0;
		});

		this.save();
		this.afterReset?.();
	}

	async save() {
		const customStats: Record<CustomStatsKey, number> = {} as any;

		Object.entries(this.customStats).map((entry) => {
			const key = entry[0] as CustomStatsKey;
			const stat = entry[1] as CustomDataPoint<LiveStatsKey, CustomStatsKey>;

			// Save just value and visible properties
			customStats[key] = numberOrZero(stat.value);
		});

		return db.stats
			.put({
				game: this._game,
				gameVariant: this._gameVariant,
				bestTime: numberOrZero(this.fixedStats.bestTime.value),
				totalTime: numberOrZero(this.fixedStats.totalTime.value),
				averageTime: numberOrZero(this.fixedStats.averageTime.value),
				totalGames: numberOrZero(this.fixedStats.totalGames.value),
				wonGames: numberOrZero(this.fixedStats.wonGames.value),
				createdAt: new Date(),
				customStats,
			})
			.catch((error) => {
				this.mustRefreshPageDueToError = true;
				handleError(error);
			});
	}

	private async savePinnedStats() {
		return db.pinnedStats
			.put({
				game: this._game,
				stats: [this.pinnedStatsKeys[0], this.pinnedStatsKeys[1]],
			})
			.catch((error) => {
				this.mustRefreshPageDueToError = true;
				handleError(error);
			});
	}

	async load() {
		try {
			const [stats, pinnedStats] = await Promise.all([
				db.stats
					.where({
						game: this._game,
						gameVariant: this._gameVariant,
					})
					.first(),
				db.pinnedStats
					.where({
						game: this._game,
					})
					.first(),
			]);

			if (stats) {
				this.fixedStats.bestTime.value = numberOrZero(stats.bestTime);
				this.fixedStats.totalTime.value = numberOrZero(stats.totalTime);
				this.fixedStats.averageTime.value = numberOrZero(stats.averageTime);
				this.fixedStats.totalGames.value = numberOrZero(stats.totalGames);
				this.fixedStats.wonGames.value = numberOrZero(stats.wonGames);

				Object.keys(this.customStats).forEach((key) => {
					const value = stats.customStats[key];

					if (value !== undefined || value !== null) {
						this.customStats[key as CustomStatsKey].value = numberOrZero(value);
					}
				});
			}

			if (pinnedStats) {
				this.pinnedStatsKeys[0] = pinnedStats.stats[0] as any;
				this.pinnedStatsKeys[1] = pinnedStats.stats[1] as any;
			}
		} catch (error: any) {
			this.mustRefreshPageDueToError = true;
			handleError(error);
		}
	}

	clone(props?: { gameVariant?: string }) {
		const originalProps: StatsProps<LiveStatsKey, CustomStatsKey, PinnedStats> = {
			game: this._game,
			gameVariant: props?.gameVariant ?? this._gameVariant,
			timer: this.timer,
			liveStats: {} as any,
			initialPinnedStats: [this.pinnedStatsKeys[0], this.pinnedStatsKeys[1]] as any,
			bestTimeStrategy: this._bestTimeStrategy,
			bestTimeSaveStrategy: this._bestTimeSaveStrategy,
			beforeReset: this.beforeReset,
			afterReset: this.afterReset,
		};

		// Extract live stats configuration from current instance
		// Object.entries(this.liveStats).forEach(([key, stat]) => {
		// 	if (key !== 'time') {
		// 		originalProps.liveStats[key as LiveStatsKey] = {
		// 			name: stat.name,
		// 			value: stat.value,
		// 			unit: stat.unit as any,
		// 			calcIsNewBest: stat.calcIsNewBest,
		// 			description: stat.description,
		// 			metrics: (stat as any).metrics,
		// 		};
		// 	}
		// });

		const cloned = new Stats<LiveStatsKey, CustomStatsKey, PinnedStats>(originalProps);

		// Load stats for the new variant
		cloned.load();

		return cloned;
	}
}
