import { type GameSoundResource } from '$lib/data/gameSounds';
import <PERSON><PERSON>on<PERSON>tti from 'js-confetti';
import { DailyGame, type DailyGameProps } from '../DailyGame.svelte';
import type { GameSound } from '../GameSound.svelte';
import { GameSoundRegistry } from '../GameSoundRegistry.svelte';
import { SettingsManager, type SettingsManagerProps } from '../SettingsManager.svelte';
import { Stats, type FixedStatsKey, type StatsProps } from '../Stats.svelte';
import { Timer } from '../Timer.svelte';
import { browser } from '$app/environment';
import type { Leaderboard } from '../Leaderboard.svelte';
import { islandSettings } from '$lib/stores/islandSettings.svelte';
import { authClient } from '$lib/auth/client';
import { getAllCombinationsFrom } from '$lib/functions/getAllCombinations';

type Constructor<T, <PERSON>rgs extends any[] = any[]> = new (...args: Args) => T;

interface DailyGameContextProps<NewGameOptions, EncodedGame>
	extends Omit<DailyGameProps<EncodedGame>, 'onPlay' | 'onError' | 'game'> {
	toProps: (encoded: any) => NewGameOptions & CreateGameOptions;
}

interface FormattedTexts {
	name: string;
	variant?: string;
	leaderboardVariant?: string;
	dailyVariant?: string;
}

type GameState = 'won' | 'lost' | 'draw';

export type ContextStats<Game, StatsLiveKeys extends string, StatsCustomKeys extends string> = {
	canUpdateWithGameLost: (game: Game) => boolean;
	visibleStats: Array<StatsCustomKeys | FixedStatsKey>;
	stats: Stats<StatsLiveKeys, StatsCustomKeys>;
};

export type ContextSounds<SoundKey extends string> = {
	resources: Record<SoundKey, GameSoundResource>;
	lifecycle?: {
		createGame?: GameSoundResource;
		win?: GameSoundResource;
		lose?: GameSoundResource;
		draw?: GameSoundResource;
	};
};

export type ContextLeaderboard<Game> = {
	sendScoreOn: GameState[];
	leaderboard: Leaderboard;
	getScore: (game: Game) => {
		/** If null, the score will not be sent */
		score: number;
		moves?: number;
	};
};

interface Props<
	Game,
	NewGameOptions,
	Settings extends Record<string, any>,
	SoundKey extends string,
	StatsLiveKeys extends string,
	StatsCustomKeys extends string,
	EncodedGame,
> {
	GameClass?: Constructor<Game, [NewGameOptions]>;
	gameKey: string;
	sounds?: ContextSounds<SoundKey>;
	formatted: (
		context: GameContext<
			Game,
			NewGameOptions,
			Settings,
			SoundKey,
			StatsLiveKeys,
			StatsCustomKeys,
			any
		>,
	) => FormattedTexts;
	settings: Omit<SettingsManagerProps<Settings>, 'key'>;
	variants?: {
		[K in keyof Settings]?: readonly Settings[K][];
	};
	formatVariant?: (variant: Settings) => string;
	dailyGame?: (
		context: GameContext<
			Game,
			NewGameOptions,
			Settings,
			SoundKey,
			StatsLiveKeys,
			StatsCustomKeys,
			any
		>,
	) =>
		| DailyGameContextProps<NewGameOptions, EncodedGame>
		| Omit<DailyGameProps<EncodedGame>, 'game'>;
	stats?: (statsProps: {
		props: Pick<StatsProps<StatsLiveKeys, StatsCustomKeys>, 'game' | 'timer'>;
		context: GameContext<
			Game,
			NewGameOptions,
			Settings,
			SoundKey,
			StatsLiveKeys,
			StatsCustomKeys,
			any
		>;
	}) => ContextStats<Game, StatsLiveKeys, StatsCustomKeys>;
	leaderboard?: (
		context: GameContext<
			Game,
			NewGameOptions,
			Settings,
			SoundKey,
			StatsLiveKeys,
			StatsCustomKeys,
			any
		>,
	) => ContextLeaderboard<Game>;
	onWillCreateGame?: (props: {
		previousGame?: Game;
		newGameOptions: NewGameOptions & CreateGameOptions;
		context: GameContext<
			Game,
			NewGameOptions,
			Settings,
			SoundKey,
			StatsLiveKeys,
			StatsCustomKeys,
			any
		>;
	}) => void;
	onGameCreated?: (props: {
		newGame: Game;
		previousGame?: Game;
		newGameOptions: NewGameOptions & CreateGameOptions;
		context: GameContext<
			Game,
			NewGameOptions,
			Settings,
			SoundKey,
			StatsLiveKeys,
			StatsCustomKeys,
			any
		>;
	}) => void;
	defaultGameProps?: (
		context: GameContext<
			Game,
			NewGameOptions,
			Settings,
			SoundKey,
			StatsLiveKeys,
			StatsCustomKeys,
			any
		>,
	) => NewGameOptions & CreateGameOptions;
	isGameReady?: (game: Game) => boolean;
	onDispose?: (
		context: GameContext<
			Game,
			NewGameOptions,
			Settings,
			SoundKey,
			StatsLiveKeys,
			StatsCustomKeys,
			any
		>,
	) => void;
}

interface CreateGameOptions {
	/**
	 * If true, will play the createGameSound, if any.
	 * By default, it does not play the sound for the first time
	 **/
	playSound?: boolean;
	/**
	 * Marks if the game to be created is a daily game. Default is false
	 */
	isDaily?: boolean;
	updateStatsIfNeeded?: boolean;
}

export class GameContext<
	Game,
	NewGameOptions,
	Settings extends Record<string, any>,
	SoundKey extends string,
	StatsLiveKeys extends string,
	StatsCustomKeys extends string,
	EncodedGame,
> {
	readonly timer = new Timer();
	readonly sounds: Record<SoundKey, GameSound>;
	private readonly confetti!: JSConfetti;
	private readonly lifecycleSounds: {
		createGame?: GameSound;
		win?: GameSound;
		lose?: GameSound;
		draw?: GameSound;
	} = {};
	private _isLoaded = $state(false);
	private _isPlayingDailyGame = $state(false);
	private _game?: Game = $state();
	private _settingsManager = $state() as SettingsManager<Settings>;
	private _stats?: Stats<StatsLiveKeys, StatsCustomKeys> = $state();
	private _dailyGame?: DailyGame<EncodedGame> = $state();
	private _leaderboardContext?: ContextLeaderboard<Game> = $state();
	private _props: Props<
		Game,
		NewGameOptions,
		Settings,
		SoundKey,
		StatsLiveKeys,
		StatsCustomKeys,
		EncodedGame
	>;
	private _gameState = $state<GameState>();
	private _canUpdateStatsWithGameLost?: (game: Game) => boolean;
	private _visibleStats = $state<Array<StatsCustomKeys | FixedStatsKey>>([]);
	private _formatted = $state<FormattedTexts>({ name: '' });
	private _previousGame?: Game;

	constructor(
		props: Props<
			Game,
			NewGameOptions,
			Settings,
			SoundKey,
			StatsLiveKeys,
			StatsCustomKeys,
			EncodedGame
		>,
	) {
		this._props = props;
		const soundProps = props.sounds;
		this.sounds = soundProps
			? GameSoundRegistry.register(soundProps.resources)
			: ({} as Record<SoundKey, GameSound>);

		if (soundProps) {
			if (soundProps.lifecycle) {
				function getKey(url: string) {
					if (!soundProps) {
						return;
					}

					const [key] =
						Object.entries(soundProps?.resources).find(
							([_key, resource]) => (resource as GameSoundResource).url === url,
						) ?? [];

					return key;
				}

				if (soundProps.lifecycle.createGame) {
					const createGameKey = getKey(soundProps.lifecycle.createGame.url);
					this.lifecycleSounds.createGame = this.sounds[createGameKey as SoundKey];
				}
				if (soundProps.lifecycle.win) {
					const winKey = getKey(soundProps.lifecycle.win.url);
					this.lifecycleSounds.win = this.sounds[winKey as SoundKey];
				}
				if (soundProps.lifecycle.lose) {
					const loseKey = getKey(soundProps.lifecycle.lose.url);
					this.lifecycleSounds.lose = this.sounds[loseKey as SoundKey];
				}
				if (soundProps.lifecycle.draw) {
					const drawKey = getKey(soundProps.lifecycle.draw.url);
					this.lifecycleSounds.draw = this.sounds[drawKey as SoundKey];
				}
			}
		}

		if (browser) {
			this.confetti = new JSConfetti();
		}

		this._settingsManager = new SettingsManager({
			...this._props.settings,
			key: this._props.gameKey,
		});
	}

	load() {
		if (browser) {
			GameSoundRegistry.loadAll(this.sounds);
			this._settingsManager?.load();

			if (this._props.dailyGame) {
				const dailyGameProps = this._props.dailyGame(this);
				this._dailyGame = new DailyGame({
					...dailyGameProps,
					game: this._props.gameKey,
					onError: () => {
						if ((dailyGameProps as DailyGameProps<EncodedGame>).onError) {
							(dailyGameProps as DailyGameProps<EncodedGame>).onError();
						} else {
							this.createGame(this._props.defaultGameProps?.(this));
						}
					},
					onPlay: (encoded) => {
						if ((dailyGameProps as DailyGameProps<EncodedGame>).onPlay) {
							(dailyGameProps as DailyGameProps<EncodedGame>).onPlay(encoded);
						} else {
							this.createGame({
								...(dailyGameProps as DailyGameContextProps<NewGameOptions, EncodedGame>).toProps(
									encoded,
								),
								isDaily: true,
							});
						}
					},
				});
				this._dailyGame.load();
				this._updateFormattedTexts();
			}

			// Create daily game or default game
			if (this._dailyGame?.urlHasDailyGame()) {
				this._dailyGame?.playDailyGameFromUrl();
			} else {
				this.createGame(this._props.defaultGameProps?.(this));
			}
		}
	}

	formatVariant(settings: Partial<Settings>) {
		return this._props.formatVariant?.({
			...this.settingsManager.settings,
			...settings,
		});
	}

	get variants() {
		return this._props.variants;
	}

	/** Get all possible settings for the given variants */
	get allVariantCombinations() {
		if (this.variants) {
			return getAllCombinationsFrom(this.variants as any);
		}

		return [];
	}

	get gameKey() {
		return this._props.gameKey;
	}

	get isGameReady() {
		if (!this.game) {
			return false;
		}

		return this._props.isGameReady?.(this.game) ?? true;
	}

	get formatted() {
		return this._formatted;
	}

	get settingsManager() {
		return this._settingsManager;
	}

	get stats() {
		return this._stats;
	}

	get dailyGame() {
		return this._dailyGame;
	}

	get isPlayingDailyGame() {
		return this._isPlayingDailyGame;
	}

	get game() {
		return this._game;
	}

	get leaderboard() {
		return this._leaderboardContext?.leaderboard;
	}

	handleGameOver(
		gameState: 'won' | 'lost' | 'draw',
		options?: {
			updateStats?: boolean;
			stopTimer?: boolean;
			handleDailyGame?: boolean;
			handleConfetti?: boolean;
			handleSound?: boolean;
		},
	) {
		const previousGameState = this._gameState;

		if (this._gameState === gameState) {
			return;
		}

		this._gameState = gameState;

		const {
			updateStats = true,
			stopTimer = true,
			handleDailyGame = true,
			handleConfetti = true,
			handleSound = true,
		} = options ?? {};

		if (gameState !== undefined) {
			// Update stats just if the change was from playing to finished (undefined to won/lost)
			if (updateStats && previousGameState === undefined) {
				this.stats?.updateStats(gameState === 'won');
			}

			if (stopTimer) {
				this.timer.stop();
			}

			if (handleDailyGame && this.isPlayingDailyGame && this.dailyGame) {
				this.dailyGame.markAsPlayed();
			}

			const session = authClient.useSession();

			if (this._leaderboardContext && this.leaderboard && session.get().data?.user) {
				const canLoadBoard =
					islandSettings.settings.leaderboards &&
					islandSettings.settings.showLeaderboardsOnGameOver;

				if (this._leaderboardContext.sendScoreOn.includes(gameState)) {
					const leaderboardToUpdate = this.leaderboard;

					leaderboardToUpdate
						.sendNewPlayerScore({
							...this._leaderboardContext.getScore(this.game ?? ({} as any)),
							time: this.timer.elapsedTime,
						})
						.then((success) => {
							if (!success && canLoadBoard && leaderboardToUpdate === this.leaderboard) {
								this.leaderboard.load();
							}
						});
				} else if (canLoadBoard) {
					this.leaderboard.load();
				}
			}
		}

		if (gameState === 'won') {
			if (handleSound) {
				this.lifecycleSounds.win?.play();
			}

			if (handleConfetti) {
				this.addConfetti();
			}
		} else if (gameState === 'lost') {
			if (handleSound) {
				this.lifecycleSounds.lose?.play();
			}
		} else if (gameState === 'draw') {
			if (handleSound) {
				this.lifecycleSounds.draw?.play();
			}
		}
	}

	addConfetti() {
		const style = getComputedStyle(document.documentElement);

		return this.confetti.addConfetti({
			confettiRadius: 4,
			confettiNumber: 400,
			confettiColors: [
				style.getPropertyValue('--confetti-1'),
				style.getPropertyValue('--confetti-2'),
				style.getPropertyValue('--confetti-3'),
				style.getPropertyValue('--confetti-4'),
				style.getPropertyValue('--confetti-5'),
				style.getPropertyValue('--confetti-6'),
			],
		});
	}

	resetGameState(options?: { resetTimer: boolean }) {
		this._gameState = undefined;

		const { resetTimer = true } = options ?? {};

		if (resetTimer) {
			this.timer.reset();
		}
	}

	get isWon() {
		return this._gameState === 'won';
	}

	get isLost() {
		return this._gameState === 'lost';
	}

	get isOver() {
		return !!this._gameState;
	}

	get visibleStats() {
		return this._visibleStats;
	}

	get isLoaded() {
		return this._isLoaded;
	}

	handleWillCreateGame(props?: Partial<NewGameOptions> & CreateGameOptions) {
		this._previousGame = this._game;
		const previousGame = this._previousGame;
		const { updateStatsIfNeeded = true } = props ?? {};

		// Update stats
		if (
			updateStatsIfNeeded &&
			this._gameState === undefined && // stats were not updated yet
			previousGame &&
			this._canUpdateStatsWithGameLost?.(previousGame)
		) {
			this.stats?.updateStats(false);
		}

		this._props.onWillCreateGame?.({
			previousGame,
			newGameOptions: this.getNewGameProps(props),
			context: this,
		});

		/**
		 * Must reset timer before creating the new game, otherwise the
		 * new game can immediately start
		 **/
		this.timer.reset();
	}

	handleGameCreated(props?: Partial<NewGameOptions> & CreateGameOptions) {
		const previousGame = this._previousGame;

		// Play create game sound
		if (this.lifecycleSounds.createGame) {
			let willPlaySound = !!previousGame;

			if (props?.playSound !== undefined) {
				willPlaySound = props.playSound;
			}

			if (willPlaySound) {
				this.lifecycleSounds.createGame.play();
			}
		}

		this._gameState = undefined;
		this._isPlayingDailyGame = !!props?.isDaily;

		// Create new stats
		if (this._props.stats) {
			const { stats, canUpdateWithGameLost, visibleStats } = this._props.stats({
				props: {
					game: this._props.gameKey,
					timer: this.timer,
				},
				context: this,
			});

			void stats.load();

			this._stats = stats;
			this._canUpdateStatsWithGameLost = (game: Game) => {
				if (this.timer.elapsedTime < 1000) {
					return false;
				}

				return canUpdateWithGameLost(game);
			};
			this._visibleStats = visibleStats;
		}

		if (this._props.leaderboard) {
			this._leaderboardContext = this._props.leaderboard(this);
		}

		this._updateFormattedTexts();

		this._props.onGameCreated?.({
			newGame: this._game!,
			previousGame,
			newGameOptions: this.getNewGameProps(props),
			context: this,
		});
	}

	async createGame(props?: Partial<NewGameOptions> & CreateGameOptions) {
		this.handleWillCreateGame(props);

		if (this._props.GameClass) {
			this._game = new this._props.GameClass(this.getNewGameProps(props));
		} else {
			this._game = {} as any;
		}

		this._isLoaded = true;
		this.handleGameCreated(props);
	}

	private getNewGameProps(props?: Partial<NewGameOptions> & CreateGameOptions) {
		return {
			...this._props.defaultGameProps?.(this),
			...props,
		} as any;
	}

	private _updateFormattedTexts() {
		this._formatted = this._props.formatted(this);
	}

	dispose() {
		// Update stats
		if (this.game && this._canUpdateStatsWithGameLost?.(this.game)) {
			this.stats?.updateStats(false);
		}

		// Dispose
		if (browser) {
			GameSoundRegistry.unloadAll(this.sounds);
			this.confetti.destroyCanvas();
		}

		this._props.onDispose?.(this);
	}

	// shallowClone(override?: Partial<typeof this._props>) {
	// 	return new GameContext({
	// 		...this._props,
	// 		...override,
	// 		sounds: undefined,
	// 		dailyGame: undefined,
	// 	});
	// }
}
